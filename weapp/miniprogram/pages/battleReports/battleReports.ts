// 战报列表页面
import { BattleReportManager } from '../../utils/battleReportManager';
import type { BattleReport } from '../../utils/battleReportManager';

Page({
  data: {
    battleReports: [] as BattleReport[],
    loading: true,
    empty: false
  },

  onLoad() {
    this.loadBattleReports();
  },

  onShow() {
    this.loadBattleReports();
  },

  /**
   * 加载战报列表
   */
  loadBattleReports() {
    this.setData({ loading: true });

    try {
      const battleReportManager = BattleReportManager.getInstance();
      const reports = battleReportManager.getAllBattleReports();

      this.setData({
        battleReports: reports,
        empty: reports.length === 0,
        loading: false
      });
    } catch (error) {
      console.error('[BattleReports] 加载战报失败:', error);
      this.setData({
        battleReports: [],
        empty: true,
        loading: false
      });
    }
  },



  /**
   * 分享战报
   */
  shareBattleReport(e: any) {
    const { index } = e.currentTarget.dataset;
    const report = this.data.battleReports[index];
    
    if (!report) return;

    const shareText = this.generateShareText(report);
    
    // 复制到剪贴板
    wx.setClipboardData({
      data: shareText,
      success: () => {
        wx.showToast({
          title: '战报已复制到剪贴板',
          icon: 'success'
        });
      }
    });
  },

  /**
   * 生成分享文本
   */
  generateShareText(report: BattleReport): string {
    const date = new Date(report.timestamp).toLocaleDateString();
    const rating = report.aiAnalysis.overallRating;
    const result = report.playerPerformance.chipsWon > 0 ? '盈利' : '亏损';
    const amount = Math.abs(report.playerPerformance.chipsWon);
    
    return `🎮 德州扑克战报分享
📅 日期: ${date}
🏆 结果: ${result} ${amount} 筹码
⭐ AI评分: ${rating}/100分
📊 表现: ${rating >= 80 ? '优秀' : rating >= 60 ? '良好' : '有待提高'}

${report.summary}

来德州扑克智能训练一起提升技术吧！`;
  },

  /**
   * 删除战报
   */
  deleteBattleReport(e: any) {
    const { index } = e.currentTarget.dataset;
    const report = this.data.battleReports[index];
    
    wx.showModal({
      title: '确认删除',
      content: '确定要删除这份战报吗？',
      success: (res) => {
        if (res.confirm) {
          // 这里应该调用删除方法，暂时只是从列表中移除
          const reports = this.data.battleReports.filter((_, i) => i !== index);
          this.setData({
            battleReports: reports,
            empty: reports.length === 0
          });
          
          wx.showToast({
            title: '删除成功',
            icon: 'success'
          });
        }
      }
    });
  },

  /**
   * 格式化时间
   */
  formatTime(timestamp: number): string {
    const date = new Date(timestamp);
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    
    if (diff < 60000) { // 1分钟内
      return '刚刚';
    } else if (diff < 3600000) { // 1小时内
      return `${Math.floor(diff / 60000)}分钟前`;
    } else if (diff < 86400000) { // 1天内
      return `${Math.floor(diff / 3600000)}小时前`;
    } else {
      return date.toLocaleDateString();
    }
  },

  /**
   * 获取评分颜色
   */
  getRatingColor(rating: number): string {
    if (rating >= 80) return '#4CAF50'; // 绿色
    if (rating >= 60) return '#FF9800'; // 橙色
    return '#F44336'; // 红色
  },

  /**
   * 获取结果图标
   */
  getResultIcon(chipsWon: number): string {
    return chipsWon > 0 ? '📈' : '📉';
  },

  /**
   * 开始游戏 - 跳转到游戏设置页面
   */
  goHome() {
    // 触觉反馈
    wx.vibrateShort({
      type: 'light'
    });

    wx.navigateTo({
      url: '/pages/setup/setup',
      success: () => {
        console.log('[BattleReports] 跳转到游戏设置页面');
      },
      fail: (error) => {
        console.error('[BattleReports] 跳转失败:', error);
        wx.showToast({
          title: '跳转失败',
          icon: 'error'
        });
      }
    });
  },

  /**
   * 页面分享
   */
  onShareAppMessage() {
    return {
      title: '我的德州扑克战报 - 智能分析助你提升技术',
      path: '/pages/index/index',
      imageUrl: '/images/share-battle-reports.jpg'
    };
  }
});
