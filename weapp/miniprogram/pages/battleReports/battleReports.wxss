/* 战报列表页面样式 */
.battle-reports-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #4CAF50 0%, #2E7D32 100%);
  padding: 20rpx;
}

/* 页面头部 */
.page-header {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.header-content {
  text-align: center;
}

.page-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 8rpx;
}

.page-subtitle {
  font-size: 26rpx;
  color: #666;
  display: block;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 0;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
  border-top: 4rpx solid #fff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 24rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  color: #fff;
  font-size: 28rpx;
}

/* 空状态 */
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 40rpx;
  text-align: center;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 32rpx;
  opacity: 0.8;
}

.empty-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #fff;
  margin-bottom: 16rpx;
  display: block;
}

.empty-subtitle {
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 48rpx;
  display: block;
  line-height: 1.5;
}

.empty-button {
  background: #fff;
  color: #4CAF50;
  border-radius: 50rpx;
  padding: 24rpx 48rpx;
  font-size: 28rpx;
  font-weight: bold;
  border: none;
}

/* 战报列表 */
.reports-list {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.report-item {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16rpx;
  padding: 32rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease;
}

.report-item:active {
  transform: scale(0.98);
}

/* 战报头部 */
.report-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24rpx;
}

.report-date {
  flex: 1;
}

.date-text {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 8rpx;
}

.game-info {
  font-size: 24rpx;
  color: #666;
  display: block;
}

.report-actions {
  display: flex;
  gap: 16rpx;
}

.action-btn {
  width: 60rpx;
  height: 60rpx;
  background: #f5f5f5;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background 0.2s ease;
}

.action-btn:active {
  background: #e0e0e0;
}

.action-icon {
  font-size: 24rpx;
}

/* 战报内容 */
.report-content {
  margin-bottom: 24rpx;
}

/* 结果概览 */
.result-overview {
  display: flex;
  justify-content: space-between;
  align-items: stretch;
  margin-bottom: 24rpx;
  padding: 24rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  min-height: 120rpx;
}

.result-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  flex: 1;
  text-align: center;
}

.result-icon {
  font-size: 32rpx;
  margin-bottom: 8rpx;
  line-height: 1;
}

.result-info {
  text-align: center;
}

.result-label {
  font-size: 22rpx;
  color: #666;
  display: block;
  margin-bottom: 8rpx;
  line-height: 1.2;
}

.result-value {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  display: block;
  line-height: 1;
}

.result-value.profit {
  color: #4CAF50;
}

.result-value.loss {
  color: #F44336;
}

.result-value.rating {
  font-size: 28rpx;
}

/* 战报摘要 */
.report-summary {
  margin-bottom: 24rpx;
}

.summary-text {
  font-size: 26rpx;
  color: #555;
  line-height: 1.6;
}

/* 关键数据 */
.key-stats {
  display: flex;
  justify-content: space-around;
  padding: 20rpx;
  background: rgba(76, 175, 80, 0.1);
  border-radius: 12rpx;
}

.stat-item {
  text-align: center;
}

.stat-label {
  font-size: 22rpx;
  color: #666;
  display: block;
  margin-bottom: 4rpx;
}

.stat-value {
  font-size: 24rpx;
  font-weight: bold;
  color: #4CAF50;
  display: block;
}

/* 详细分析内容 */
.detail-analysis {
  margin-top: 24rpx;
  padding-top: 24rpx;
  border-top: 1rpx solid #e0e0e0;
}

.analysis-section {
  margin-bottom: 24rpx;
}

.section-title {
  font-size: 26rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 16rpx;
}

.analysis-list {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.analysis-item {
  display: flex;
  align-items: flex-start;
  gap: 12rpx;
  padding: 16rpx;
  border-radius: 8rpx;
}

.analysis-item.strength {
  background: rgba(76, 175, 80, 0.1);
}

.analysis-item.weakness {
  background: rgba(255, 152, 0, 0.1);
}

.analysis-item.recommendation {
  background: rgba(33, 150, 243, 0.1);
}

.analysis-icon {
  font-size: 20rpx;
  line-height: 1.5;
}

.analysis-text {
  flex: 1;
  font-size: 24rpx;
  color: #333;
  line-height: 1.5;
}

.summary-text {
  font-size: 24rpx;
  color: #555;
  line-height: 1.6;
}



/* 底部提示 */
.bottom-tip {
  text-align: center;
  padding: 40rpx 20rpx;
}

.tip-text {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.5;
}
