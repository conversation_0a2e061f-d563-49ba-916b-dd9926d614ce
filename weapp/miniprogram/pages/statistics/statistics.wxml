<!--统计页面-->
<view class="container">
  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading">
    <text>加载中...</text>
  </view>

  <!-- 主要内容 -->
  <view wx:else class="content">
    <!-- 用户信息卡片 -->
    <view class="user-card">
      <view class="user-info">
        <image class="avatar" src="{{userProfile.avatar}}" mode="aspectFill"></image>
        <view class="user-details">
          <text class="nickname">{{userProfile.nickname}}</text>
          <text class="level">Lv.{{userLevel.level}} {{userLevel.name}}</text>
          <view class="experience">
            <text>经验: {{userProfile.experience}}/{{userLevel.maxExperience}}</text>
            <view class="exp-bar">
              <view class="exp-progress" style="width: {{(userProfile.experience - userLevel.minExperience) / (userLevel.maxExperience - userLevel.minExperience) * 100}}%"></view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 时间周期选择 -->
    <view class="period-selector">
      <view class="period-item {{selectedPeriod === 'week' ? 'active' : ''}}" 
            bindtap="onPeriodChange" data-period="week">
        <text>本周</text>
      </view>
      <view class="period-item {{selectedPeriod === 'month' ? 'active' : ''}}" 
            bindtap="onPeriodChange" data-period="month">
        <text>本月</text>
      </view>
      <view class="period-item {{selectedPeriod === 'all' ? 'active' : ''}}" 
            bindtap="onPeriodChange" data-period="all">
        <text>全部</text>
      </view>
    </view>

    <!-- 核心统计 -->
    <view class="stats-section">
      <view class="section-title">核心数据</view>
      <view class="stats-grid">
        <view class="stat-item" bindtap="showStatDetail" data-stat="winRate">
          <text class="stat-value">{{winRateDisplay}}%</text>
          <text class="stat-label">胜率</text>
        </view>
        <view class="stat-item" bindtap="showStatDetail" data-stat="totalGames">
          <text class="stat-value">{{statistics.totalGames || 0}}</text>
          <text class="stat-label">总局数</text>
        </view>
        <view class="stat-item" bindtap="showStatDetail" data-stat="avgProfit">
          <text class="stat-value {{avgProfitClass}}">{{avgProfitDisplay}}</text>
          <text class="stat-label">平均盈利</text>
        </view>
        <view class="stat-item" bindtap="showStatDetail" data-stat="totalPlayTime">
          <text class="stat-value">{{playTimeDisplay}}h</text>
          <text class="stat-label">游戏时长</text>
        </view>
      </view>
    </view>

    <!-- 操作习惯 -->
    <view class="stats-section">
      <view class="section-title">操作习惯</view>
      <view class="habit-stats">
        <view class="habit-item">
          <text class="habit-label">入池率 (VPIP)</text>
          <view class="habit-bar">
            <view class="habit-progress" style="width: {{vpipWidth}}%"></view>
          </view>
          <text class="habit-value">{{vpipDisplay}}%</text>
        </view>
        <view class="habit-item">
          <text class="habit-label">翻牌前加注率 (PFR)</text>
          <view class="habit-bar">
            <view class="habit-progress" style="width: {{pfrWidth}}%"></view>
          </view>
          <text class="habit-value">{{pfrDisplay}}%</text>
        </view>
        <view class="habit-item">
          <text class="habit-label">激进度</text>
          <view class="habit-bar">
            <view class="habit-progress" style="width: {{aggressionWidth}}%"></view>
          </view>
          <text class="habit-value">{{aggressionDisplay}}%</text>
        </view>
      </view>
    </view>

    <!-- AI使用统计 -->
    <view class="stats-section">
      <view class="section-title">智能助手使用</view>
      <view class="ai-stats">
        <view class="ai-item">
          <text class="ai-label">提示使用次数</text>
          <text class="ai-value">{{statistics.aiStats ? statistics.aiStats.hintsUsed : 0}}</text>
        </view>
        <view class="ai-item">
          <text class="ai-label">教学模式游戏</text>
          <text class="ai-value">{{statistics.aiStats ? statistics.aiStats.teachingModeGames : 0}}</text>
        </view>
        <view class="ai-item">
          <text class="ai-label">平均评分</text>
          <text class="ai-value">{{avgAIScoreDisplay}}</text>
        </view>
      </view>
    </view>

    <!-- 趋势图表 -->
    <view class="stats-section" wx:if="{{trendData.length > 0}}">
      <view class="section-title">盈利趋势</view>
      <view class="trend-chart">
        <view class="chart-container">
          <!-- 简单的趋势显示，实际项目中可以使用图表库 -->
          <view class="trend-line">
            <block wx:for="{{trendData}}" wx:key="date">
              <view class="trend-point {{item.profit >= 0 ? 'positive' : 'negative'}}"
                    style="left: {{trendPointPosition[index]}}%">
              </view>
            </block>
          </view>
        </view>
        <view class="trend-labels">
          <text class="trend-start">{{trendData[0] ? trendData[0].date : ''}}</text>
          <text class="trend-end">{{trendData[trendData.length - 1] ? trendData[trendData.length - 1].date : ''}}</text>
        </view>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="action-buttons">
      <button class="action-btn primary" bindtap="goToGame">开始游戏</button>
      <button class="action-btn secondary" bindtap="goToProfile">个人资料</button>
      <button class="action-btn secondary" bindtap="shareStats">分享战绩</button>
    </view>
  </view>

  <!-- 详细信息弹窗 -->
  <view class="modal" wx:if="{{showDetailModal}}" bindtap="closeDetailModal">
    <view class="modal-content" catchtap="">
      <view class="modal-header">
        <text class="modal-title">详细说明</text>
        <text class="modal-close" bindtap="closeDetailModal">×</text>
      </view>
      <view class="modal-body">
        <text>{{selectedStat}}</text>
      </view>
    </view>
  </view>
</view>
