# 🎮 德州扑克小程序

一个功能完整的德州扑克小程序，集成了智能AI对手、教学系统、统计分析等功能。

## 📋 项目概述

### 核心功能
- 🎯 **完整的德州扑克游戏** - 支持2-8人游戏，完整的游戏规则
- 🤖 **智能AI对手** - 多种AI策略，不同难度级别
- 🎓 **教学系统** - 实时指导，决策分析，学习进度跟踪
- 📊 **统计分析** - 详细的游戏数据和表现分析
- 🦑 **鱿鱼模式** - 特殊游戏模式，增加游戏趣味性
- 👤 **用户系统** - 个人资料，成就系统，学习记录

### 技术特点
- 💻 **微信小程序** - 基于微信小程序框架开发
- 🎨 **现代UI设计** - 精美的界面和流畅的动画效果
- 🧠 **AI集成** - 集成大语言模型提供智能建议
- 📱 **响应式设计** - 适配不同屏幕尺寸
- 🔄 **实时更新** - 游戏状态实时同步

## 🗂️ 项目结构

```
weapp/
├── miniprogram/                    # 小程序源码
│   ├── pages/                      # 页面文件
│   │   ├── index/                  # 首页
│   │   ├── game/                   # 游戏主页面
│   │   ├── setup/                  # 游戏设置
│   │   ├── teaching/               # 教学页面
│   │   ├── statistics/             # 统计页面
│   │   └── profile/                # 个人资料
│   ├── utils/                      # 工具类
│   │   ├── gameUtils.ts            # 游戏工具函数
│   │   ├── teachingManager.ts      # 教学管理器
│   │   ├── userManager.ts          # 用户管理器
│   │   ├── statisticsManager.ts    # 统计管理器
│   │   └── pokerHand.ts           # 牌型计算
│   ├── types/                      # 类型定义
│   ├── constants/                  # 常量定义
│   └── components/                 # 自定义组件
├── docs/                          # 文档目录
└── README.md                      # 项目说明
```

## 🎓 教学系统详解

教学系统是本项目的核心特色功能，为玩家提供智能化的学习指导。

### 📖 详细文档
👉 **[教学系统完整指南](miniprogram/TEACHING_SYSTEM_GUIDE.md)**

### 🔥 核心特性
- **实时教学指导** - 游戏过程中提供即时的策略建议
- **智能决策分析** - AI分析当前局面，给出最优决策
- **个性化学习** - 根据玩家水平调整教学内容
- **进度跟踪** - 记录学习进度和改进情况

### 🎯 教学内容分类
- **基础规则** - 游戏规则和基本概念
- **翻牌前策略** - 起手牌选择和位置策略
- **翻牌后游戏** - 牌面分析和决策技巧
- **高级技巧** - 数学计算和心理战术

## 🚀 快速开始

### 环境要求
- Node.js 14+
- 微信开发者工具
- TypeScript 4+

### 安装步骤
1. **克隆项目**
   ```bash
   git clone [项目地址]
   cd weapp
   ```

2. **安装依赖**
   ```bash
   npm install
   ```

3. **开发者工具**
   - 打开微信开发者工具
   - 导入项目目录 `weapp`
   - 选择小程序项目类型

4. **配置设置**
   - 配置AppID（如需真机调试）
   - 设置开发环境参数

### 开发调试
- **本地调试**: 使用微信开发者工具的模拟器
- **真机调试**: 扫码在手机上测试
- **数据库**: 使用本地存储模拟数据持久化

## 📚 功能模块说明

### 🎮 游戏核心
- **游戏引擎** - 完整的德州扑克规则实现
- **AI系统** - 多种AI策略和难度级别
- **动画系统** - 流畅的卡牌和筹码动画
- **音效系统** - 丰富的游戏音效反馈

### 🎓 教学系统
- **实时指导** - 操作前的策略建议
- **决策评估** - 操作后的决策分析
- **学习课程** - 结构化的学习内容
- **进度跟踪** - 学习进度和成就系统

### 📊 数据分析
- **游戏统计** - 胜率、盈利等关键指标
- **行为分析** - 决策模式和改进建议
- **历史记录** - 详细的游戏历史数据
- **趋势分析** - 长期表现趋势图表

### 👤 用户系统
- **个人资料** - 用户信息和偏好设置
- **成就系统** - 游戏成就和里程碑
- **等级系统** - 基于经验的等级进阶
- **社交功能** - 分享和排行榜

## 🔧 技术架构

### 前端技术栈
- **框架**: 微信小程序原生框架
- **语言**: TypeScript
- **样式**: WXSS + CSS3动画
- **状态管理**: 页面级状态管理
- **工具库**: 自研工具函数

### 核心算法
- **牌型计算** - 7选5最佳牌型算法
- **AI决策** - 基于数学期望的决策树
- **教学分析** - 多维度游戏状态分析
- **统计计算** - 实时统计指标计算

### 数据存储
- **本地存储** - 微信小程序本地存储API
- **数据管理** - 统一的数据管理器
- **缓存策略** - 智能缓存提升性能
- **数据同步** - 跨页面数据同步机制

## 📈 项目亮点

### 🎯 创新功能
1. **智能教学系统** - 业界首创的实时教学指导
2. **AI决策建议** - 集成大语言模型的智能建议
3. **多维度分析** - 全方位的游戏数据分析
4. **个性化学习** - 根据用户水平定制内容

### 💡 技术优势
1. **高性能** - 优化的算法和缓存策略
2. **用户体验** - 流畅的动画和直观的界面
3. **可扩展性** - 模块化设计，易于扩展
4. **稳定性** - 完善的错误处理和降级机制

### 🏆 商业价值
1. **教育价值** - 帮助用户学习德州扑克技巧
2. **娱乐价值** - 提供高质量的游戏体验
3. **社交价值** - 促进用户交流和分享
4. **数据价值** - 积累用户行为和偏好数据

## 📋 开发规范

### 代码规范
- **TypeScript** - 严格的类型检查
- **ESLint** - 代码质量检查
- **Prettier** - 代码格式化
- **注释规范** - 详细的函数和类注释

### 文件组织
- **模块化** - 功能模块独立开发
- **组件化** - 可复用的UI组件
- **工具化** - 通用工具函数抽离
- **类型化** - 完整的TypeScript类型定义

### 性能优化
- **代码分割** - 按需加载减少包体积
- **缓存策略** - 智能缓存提升响应速度
- **动画优化** - 硬件加速的流畅动画
- **内存管理** - 及时清理避免内存泄漏

## 🤝 贡献指南

### 参与方式
1. **Issue反馈** - 报告bug或提出功能建议
2. **代码贡献** - 提交Pull Request
3. **文档完善** - 改进项目文档
4. **测试反馈** - 参与功能测试

### 开发流程
1. Fork项目到个人仓库
2. 创建功能分支进行开发
3. 提交代码并创建Pull Request
4. 代码审查通过后合并

## 📞 联系方式

- **项目地址**: [GitHub仓库链接]
- **问题反馈**: [Issues页面链接]
- **技术交流**: [讨论区链接]

## 📄 许可证

本项目采用 MIT 许可证，详见 [LICENSE](LICENSE) 文件。

---

**🎉 感谢使用德州扑克小程序！希望这个项目能帮助你学习和享受德州扑克的乐趣。**
