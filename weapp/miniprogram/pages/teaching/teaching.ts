// AI教学页面
import { TeachingManager } from '../../utils/teachingManager';
import { UserManager } from '../../utils/userManager';

Page({
  data: {
    lessons: [] as any[],
    selectedLesson: null as any,
    showLessonDetail: false,

    // 教学模式设置
    teachingSettings: {
      realTimeHints: true,
      detailedAnalysis: true,
      mistakeCorrection: true,
      difficultyLevel: 'beginner' as 'beginner' | 'intermediate' | 'advanced'
    },

    // 学习进度
    learningProgress: {
      completedLessons: 0,
      totalLessons: 0,
      currentLevel: 'beginner',
      nextMilestone: ''
    },

    // 格式化显示数据
    progressPercent: 0,
    difficultyIndex: 0,

    loading: true
  },

  onLoad() {
    this.loadTeachingData();
  },

  /**
   * 加载教学数据
   */
  loadTeachingData() {
    this.setData({ loading: true });
    
    try {
      const teachingManager = TeachingManager.getInstance();
      const lessons = teachingManager.getTeachingLessons();
      
      // 获取用户学习进度
      const userManager = UserManager.getInstance();
      const userProfile = userManager.getCurrentUser();
      
      const learningProgress = {
        completedLessons: 0, // 这里可以从用户数据中获取
        totalLessons: lessons.length,
        currentLevel: 'beginner',
        nextMilestone: '完成基础课程'
      };
      
      // 计算进度百分比
      const progressPercent = learningProgress.totalLessons > 0 ?
        Math.floor((learningProgress.completedLessons / learningProgress.totalLessons) * 100) : 0;

      // 计算难度索引
      const difficultyLevels = ['beginner', 'intermediate', 'advanced'];
      const difficultyIndex = difficultyLevels.indexOf(this.data.teachingSettings.difficultyLevel);

      this.setData({
        lessons,
        learningProgress,
        progressPercent,
        difficultyIndex,
        loading: false
      });
      
    } catch (error) {
      console.error('[Teaching] 加载教学数据失败:', error);
      this.setData({ loading: false });
    }
  },

  /**
   * 选择课程
   */
  selectLesson(e: any) {
    const { index } = e.currentTarget.dataset;
    const lesson = this.data.lessons[index];
    
    this.setData({
      selectedLesson: lesson,
      showLessonDetail: true
    });
  },

  /**
   * 关闭课程详情
   */
  closeLessonDetail() {
    this.setData({ showLessonDetail: false });
  },

  /**
   * 开始学习课程
   */
  startLesson() {
    const { selectedLesson } = this.data;
    if (!selectedLesson) return;
    
    // 根据课程类型跳转到不同的学习模式
    if (selectedLesson.id === 'basics') {
      // 基础课程 - 跳转到教学模式游戏
      wx.navigateTo({
        url: `/pages/setup/setup?teachingMode=1&lessonId=${selectedLesson.id}`
      });
    } else {
      // 其他课程 - 跳转到特定的教学游戏
      wx.navigateTo({
        url: `/pages/game/game?teachingMode=1&lessonId=${selectedLesson.id}&playerCount=3&startingChips=1000`
      });
    }
  },

  /**
   * 设置项改变
   */
  onSettingChange(e: any) {
    const { setting } = e.currentTarget.dataset;
    const value = e.detail.value;
    
    this.setData({
      [`teachingSettings.${setting}`]: value
    });
    
    // 保存设置到用户配置
    const userManager = UserManager.getInstance();
    userManager.updateSettings({
      [`teaching_${setting}`]: value
    });
  },

  /**
   * 难度级别改变
   */
  onDifficultyChange(e: any) {
    const difficulty = e.detail.value;
    const levels = ['beginner', 'intermediate', 'advanced'];
    
    this.setData({
      'teachingSettings.difficultyLevel': levels[difficulty]
    });
  },

  /**
   * 获取课程进度百分比
   */
  getLessonProgress(lesson: any): number {
    // 这里可以根据用户的学习记录计算进度
    return 0;
  },

  /**
   * 获取难度标签
   */
  getDifficultyLabel(difficulty: string): string {
    const labels = {
      'beginner': '初级',
      'intermediate': '中级',
      'advanced': '高级'
    };
    return labels[difficulty as keyof typeof labels] || '未知';
  },

  /**
   * 获取难度颜色
   */
  getDifficultyColor(difficulty: string): string {
    const colors = {
      'beginner': '#28a745',
      'intermediate': '#ffc107',
      'advanced': '#dc3545'
    };
    return colors[difficulty as keyof typeof colors] || '#6c757d';
  },

  /**
   * 开始快速练习
   */
  startQuickPractice() {
    wx.navigateTo({
      url: '/pages/game/game?teachingMode=1&quickPractice=1&playerCount=3&startingChips=1000'
    });
  },

  /**
   * 查看学习统计
   */
  viewLearningStats() {
    wx.navigateTo({
      url: '/pages/statistics/statistics?tab=learning'
    });
  },

  /**
   * 分享学习进度
   */
  shareLearningProgress() {
    const { learningProgress } = this.data;
    const progressPercent = Math.floor((learningProgress.completedLessons / learningProgress.totalLessons) * 100);
    
    wx.showShareMenu({
      withShareTicket: true,
      menus: ['shareAppMessage', 'shareTimeline']
    });
    
    console.log(`我的德州扑克学习进度：${progressPercent}% (${learningProgress.completedLessons}/${learningProgress.totalLessons})`);
  },

  /**
   * 重置学习进度
   */
  resetProgress() {
    wx.showModal({
      title: '确认重置',
      content: '重置后所有学习进度将被清空，确定要重置吗？',
      success: (res) => {
        if (res.confirm) {
          // 这里可以添加重置逻辑
          wx.showToast({
            title: '重置成功',
            icon: 'success'
          });
          this.loadTeachingData();
        }
      }
    });
  },

  /**
   * 页面分享
   */
  onShareAppMessage() {
    const completedLessons = this.data.lessons.filter(l => l.completed).length;
    const totalLessons = this.data.lessons.length;
    const progress = Math.round((completedLessons / totalLessons) * 100);

    return {
      title: `🎓 德州扑克智能教学 - 学习进度${progress}% (${completedLessons}/${totalLessons})`,
      path: '/pages/teaching/teaching',
      imageUrl: '/images/share-teaching.jpg'
    };
  }
});
