import { GAME_CONSTANTS } from '../constants/gameConstants';
export const actionTextMap: { [key: string]: string | ((amount?: number) => string) } = {
  fold: '弃牌',
  check: '看牌',
  call: (amount?: number) => amount ? `跟注${amount}` : '跟注',
  raise: (amount?: number) => amount ? `加注${amount}` : '加注',
  allin: '全下',
  smallBlind: '小盲',
  bigBlind: '大盲',
  bet: (amount?: number) => amount ? `下注${amount}` : '下注'
};

export function getActionText(action: string, amount?: number) {
  const val = actionTextMap[action];
  return typeof val === 'function' ? val(amount) : val || '';
}


export function actionHistoryToText(history: any, phaseMap: Record<string, string>) {
  let txt = '';
  for (const phase of ['preFlop','flop','turn','river']) {
    if (history[phase] && history[phase].length > 0) {
      txt += `【${phaseMap[phase]}】\n`;
      for (const act of history[phase]) {
        txt += `  ${act.playerName}: ${getActionText(act.action, act.amount)}`;
        txt += '\n';
      }
    }
  }
  return txt.trim();
}


export function resetPlayersForNewHand(players: any[], dealerIndex: number, smallBlindIndex: number, bigBlindIndex: number) {
  players.forEach((p: any, i: number) => {
    p.hand = [];
    p.bet = 0;
    if (p.chips <= 0) {
      p.folded = true;
      p.isAllIn = true;
      p.eliminated = true;
    } else {
      p.folded = false;
      p.isAllIn = false;
      p.eliminated = false;
    }
    p.isDealer = i === dealerIndex;
    p.isSmallBlind = i === smallBlindIndex;
    p.isBigBlind = i === bigBlindIndex;
    p.isAI = i !== 0;
  });
}

// 主池和边池分配
export function distributePot(gameState: any, determineWinners: (players?: any[]) => any, determineWinnersForPlayers: (players: any[]) => any) {
  const playerWin: { [id: number]: number } = {};
  gameState.players.forEach((p: any) => { playerWin[p.id] = 0; });
  const winners = determineWinners();
  const mainWin = gameState.pot > 0 && winners.length > 0 ? Math.floor(gameState.pot / winners.length) : 0;
  winners.forEach((w: any) => { playerWin[w.id] += mainWin; });
  for (const sidePot of gameState.sidePots || []) {
    const eligible = gameState.players.filter((p: any) => !p.folded && p.bet >= sidePot.threshold);
    const sideWinners = determineWinnersForPlayers(eligible);
    const sideWin = sidePot.amount > 0 && sideWinners.length > 0 ? Math.floor(sidePot.amount / sideWinners.length) : 0;
    sideWinners.forEach((w: any) => { playerWin[w.id] += sideWin; });
  }
  return playerWin;
}

// 位置名辅助 - 只考虑未淘汰玩家
export function getPlayerPositionName(dealerIndex: number, playerIndex: number, players: any[]): string {
  // 过滤出未淘汰的玩家
  const activePlayers = players.filter(p => !p.eliminated && p.chips > 0);
  const activePlayerCount = activePlayers.length;

  // 如果玩家已淘汰，返回淘汰状态
  const currentPlayer = players[playerIndex];
  if (currentPlayer.eliminated || currentPlayer.chips <= 0) {
    return '已淘汰';
  }

  // 找到当前玩家在活跃玩家中的索引
  const activePlayerIndex = activePlayers.findIndex(p => p.id === currentPlayer.id);
  if (activePlayerIndex === -1) {
    return '已淘汰';
  }

  // 找到庄家在活跃玩家中的索引
  const activeDealerIndex = activePlayers.findIndex(p => p.id === players[dealerIndex].id);
  if (activeDealerIndex === -1) {
    return '位置未知';
  }

  // 计算相对位置
  const pos = (activePlayerIndex - activeDealerIndex + activePlayerCount) % activePlayerCount;

  if (activePlayerCount === 2) {
    return pos === 0 ? '庄家/小盲' : '大盲';
  }
  if (activePlayerCount === 3) {
    return ['庄家', '小盲', '大盲'][pos];
  }
  if (activePlayerCount === 4) {
    return ['庄家', '小盲', '大盲', '枪口'][pos];
  }
  if (activePlayerCount === 5) {
    return ['庄家', '小盲', '大盲', '枪口', '劫位'][pos];
  }
  if (activePlayerCount === 6) {
    return ['庄家', '小盲', '大盲', '枪口', '高位', '劫位'][pos];
  }
  if (activePlayerCount === 7) {
    return ['庄家', '小盲', '大盲', '枪口', '枪口后', '高位', '劫位'][pos];
  }
  if (activePlayerCount === 8) {
    return ['庄家', '小盲', '大盲', '枪口', '枪口后', '中位', '高位', '劫位'][pos];
  }
  if (activePlayerCount === 9) {
    return ['庄家', '小盲', '大盲', '枪口', '枪口后', '枪口后2', '中位', '高位', '劫位'][pos];
  }
  return `位置${pos}`;
}

// 7选5组合，最佳牌型
export function getBestHandCards(allCards: { suit: string; value: string }[]) {
  function comb<T>(arr: T[], k: number): T[][] {
    if (k === 0) return [[]];
    if (arr.length < k) return [];
    const [first, ...rest] = arr;
    const withFirst: T[][] = comb(rest, k - 1).map((c: T[]) => [first, ...c]);
    const withoutFirst: T[][] = comb(rest, k);
    return withFirst.concat(withoutFirst);
  }
  // 注意：evaluateHand, compareHands 需在主文件 import
  // 这里假设全局有 require
  const { evaluateHand, compareHands } = require('../utils/pokerHand');
  const all5: { suit: string; value: string }[][] = comb(allCards, 5);
  let best: { suit: string; value: string }[] | null = null, bestEval: any = null;
  for (const five of all5) {
    const eval5 = evaluateHand(five);
    if (!bestEval || compareHands(eval5, bestEval) > 0) {
      best = five;
      bestEval = eval5;
    }
  }
  return { cards: best, handEval: bestEval };
}

// actionHistory 维护
export function addActionHistory(gameState: any, phase: string, actObj: any) {
  if (!gameState.actionHistory[phase]) gameState.actionHistory[phase] = [];
  gameState.actionHistory[phase].push(actObj);
}
export function clearPlayersActedForPhase(gameState: any, phase: string) {
  if (gameState.playersActedByPhase && phase && gameState.playersActedByPhase[phase]) {
    gameState.playersActedByPhase[phase] = [];
  }
}

// 通用倒计时
export function startCountdown(that: any, key: 'actionCountdown' | 'countdown', seconds: number, onTimeout: () => void) {
  if (that.data[`${key}Timer`]) clearInterval(that.data[`${key}Timer`]);
  that.setData({ [key]: seconds });
  const timer = setInterval(() => {
    let c = that.data[key] - 1;
    if (c <= 0) {
      clearInterval(that.data[`${key}Timer`]);
      that.setData({ [`${key}Timer`]: null, [key]: 0 });
      onTimeout();
    } else {
      that.setData({ [key]: c });
    }
  }, 1000);
  that.setData({ [`${key}Timer`]: timer });
}

// 批量重置 UI 状态
export function resetGameUIState(that: any) {
  that.setData({
    showEndHandPopup: false,
    endHandResult: [],
    showdownPlayers: [],
    myResult: {},
    bettingAnimations: [],
    raiseAmount: 0,
    raiseInputValue: '',
    showRaiseModal: false,
    communityCardsFlip: [],
    countdown: 30,
    actionCountdown: 30,
    aiSuggestion: null,
    aiThinking: false,
    decisionScore: null,
    decisionScoreReason: '',
    showAiReasonModal: false,
    showScoreReasonModal: false,
    lastActionBubble: null,
    showNewHandBtn: false
  });
} 

// 构造 LLM prompt
export function buildLLMPrompt({ gameState, player, phaseMap, getBestHandCards, actionHistoryToText }: {
  gameState: any,
  player: any,
  phaseMap: any,
  getBestHandCards: any,
  actionHistoryToText: any
}) {
  const players = gameState.players;
  const community = gameState.communityCards;
  const playerCount = players.length;
  const startingChips = gameState.startingChips || 1000;
  const smallBlind = gameState.smallBlind || 20;
  const bigBlind = gameState.bigBlind || 40;
  function getPosName(idx: number, dealerIdx: number, playerCount: number) {
    if (idx === dealerIdx) return '庄家';
    if (playerCount === 2) return idx === (dealerIdx + 1) % 2 ? '大盲' : '小盲';
    if (idx === (dealerIdx + 1) % playerCount) return '小盲';
    if (idx === (dealerIdx + 2) % playerCount) return '大盲';
    if (playerCount > 3) {
      if (idx === (dealerIdx + playerCount - 1) % playerCount) return 'CO';
      if (idx === (dealerIdx + playerCount - 2) % playerCount) return 'HJ';
    }
    return '';
  }
  const playersInfo = players.map((p: any, i: number) => {
    const pos = getPosName(i, gameState.dealerIndex, players.length);
    let status = '';
    if (p.folded) status = '(已弃牌)';
    else if (p.isAllIn) status = '(已全下)';
    return `*${p.name}${pos ? '(' + pos + ')' : ''}: 剩余筹码${p.chips}${status}`;
  }).join('\n');
  let communityStr = '';
  if (gameState.gamePhase === GAME_CONSTANTS.GAME_PHASES.PRE_FLOP || community.length === 0) {
    communityStr = '无';
  } else {
    communityStr = community.map((c: any) => c.value + c.suit).join(', ');
  }
  let sidePotsStr = '';
  if (gameState.sidePots && gameState.sidePots.length > 0) {
    sidePotsStr = '\n边池情况: ' + gameState.sidePots.map((pot: any, i: number) => `边池${i+1}: ${pot.amount}筹码`).join(', ');
  }
  const canCheck = player.bet === gameState.currentBet;
  const canRaise = player.chips > (gameState.currentBet - player.bet);
  const minRaiseAmount = Math.max(gameState.currentBet * 2, gameState.currentBet + 1);
  const actionHistoryText = actionHistoryToText(gameState.actionHistory, phaseMap);
  let prompt = '';
  if (player.id === 0) {
    prompt = `你现在是我的德州扑克导师。假设我刚拿到如下手牌，请你用导师的身份，基于当前牌局信息，给我详细的建议和理由。请用第二人称\"你\"来称呼我。`;
    prompt += `\n当前阶段: ${phaseMap[gameState.gamePhase] || gameState.gamePhase}`;
    prompt += `\n公共牌: ${communityStr}`;
    prompt += `\n当前底池大小: ${gameState.pot}`;
    prompt += `\n当前最高注: ${gameState.currentBet}`;
    prompt += `\n跟注所需筹码: ${gameState.currentBet - player.bet}`;
    prompt += `\n我的剩余筹码: ${player.chips}`;
    prompt += `\n最小加注金额: ${minRaiseAmount} (必须至少是当前最高注的2倍)`;
    prompt += sidePotsStr;
    prompt += `\n\n玩家情况:\n${playersInfo}`;
    const handStr = player.hand.map((c: any) => c.value + c.suit).join(', ');
    let handType = '';
    if (player.hand && player.hand.length === 2 && community.length >= 3) {
      const allCards = player.hand.concat(community);
      const best = getBestHandCards(allCards);
      if (best.handEval && best.handEval.name) {
        handType = best.handEval.name;
      }
    }
    prompt += `\n我的手牌: ${handStr}`;
    if (handType) {
      prompt += `\n加公共牌组合牌力：${handType}`;
    }
    prompt += `\n\n可选行动:`;
    if (canCheck) {
      prompt += `\n- 看牌 (check)`;
      if (gameState.currentBet - player.bet === 0) {
        prompt += `\n- 跟注 (call) 0 筹码`;
      }
    } else {
      prompt += `\n- 跟注 (call) ${gameState.currentBet - player.bet} 筹码`;
    }
    if (canRaise) {
      prompt += `\n- 加注 (raise) 至少到 ${minRaiseAmount} 筹码`;
    }
    prompt += `\n- 弃牌 (fold)`;
    if (player.chips > 0) {
      prompt += `\n- 全下 (all-in) ${player.chips} 筹码`;
    }
    prompt += `\n\n请以导师身份，直接对我说：\"你应该如何如何……\"，并详细说明理由。`;
    prompt += `\n请结合我的手牌、位置、对手行为、筹码深度、底池赔率、玩家风格等因素，给出具体建议。`;
    prompt += `\n如果有多种可行方案，请说明各自优劣，并给出你最推荐的行动。`;
    prompt += `\n\n请以JSON格式回复，包含action和reason字段：\n{\n  \"action\": \"FOLD\",\n  \"reason\": \"你应该弃牌，因为……\"\n}\n\n或者加注示例:\n{\n  \"action\": \"RAISE\",\n  \"amount\": 100,\n  \"reason\": \"你应该加注到100，因为……\"\n}`;
    if (actionHistoryText) {
      prompt += `\n\n本局每轮玩家行动历史（含所有阶段）：\n${actionHistoryText}`;
    }
  } else {
    prompt = `你是德州扑克游戏中的玩家,名字为\"${player.name}\"。`;
    prompt += `\n当前阶段: ${phaseMap[gameState.gamePhase] || gameState.gamePhase}`;
    prompt += `\n公共牌: ${communityStr}`;
    prompt += `\n当前底池大小: ${gameState.pot}`;
    prompt += `\n当前最高注: ${gameState.currentBet}`;
    prompt += `\n跟注所需筹码: ${gameState.currentBet - player.bet}`;
    prompt += `\n你的剩余筹码: ${player.chips}`;
    prompt += `\n最小加注金额: ${minRaiseAmount} (必须至少是当前最高注的2倍)`;
    prompt += sidePotsStr;
    prompt += `\n\n玩家情况:\n${playersInfo}`;
    const handStr2 = player.hand.map((c: any) => c.value + c.suit).join(', ');
    let handType2 = '';
    if (player.hand && player.hand.length === 2 && community.length >= 3) {
      const allCards = player.hand.concat(community);
      const best = getBestHandCards(allCards);
      if (best.handEval && best.handEval.name) {
        handType2 = best.handEval.name;
      }
    }
    prompt += `\n你的手牌: ${handStr2}`;
    if (handType2) {
      prompt += `\n加公共牌组合牌力：${handType2}`;
    }
    prompt += `\n\n你的可选行动:`;
    if (canCheck) {
      prompt += `\n- 看牌 (check)`;
      if (gameState.currentBet - player.bet === 0) {
        prompt += `\n- 跟注 (call) 0 筹码`;
      }
    } else {
      prompt += `\n- 跟注 (call) ${gameState.currentBet - player.bet} 筹码`;
    }
    if (canRaise) {
      prompt += `\n- 加注 (raise) 至少到 ${minRaiseAmount} 筹码`;
    }
    prompt += `\n- 弃牌 (fold)`;
    if (player.chips > 0) {
      prompt += `\n- 全下 (all-in) ${player.chips} 筹码`;
    }
    prompt += `\n\n决策建议：如果你的手牌很弱、原本倾向于弃牌，但此时可以check（看牌），建议优先选择check而不是弃牌。`;
    prompt += `\n但如果你有较强牌力、听牌、位置优势，或有下注/诈唬的理由，请主动下注或加注，不要机械check。`;
    prompt += `\n例如：你有顶对、两对、三条、强听牌时可以主动下注。也可以在对手表现弱势时尝试小额下注或诈唬。`;
    prompt += `\n请根据真实牌局情况灵活决策。`;
    prompt += `\n\n价值下注建议：如果你认为自己的牌力领先对手，或者对手表现出弱势（如多次check），请主动下注或加注以争取更多价值。不要错失通过下注获得更多筹码的机会。即使你的牌不是最强，也可以通过下注让对手弃牌，从而赢得底池。`;
    prompt += `\n在有利位置时，适当利用位置优势进行价值下注或半诈唬。`;
    prompt += `\n你可以适当用更宽的范围入池，尤其在位置较好或对手较弱时。可以参考GTO（博弈论最优）理论，部分边缘牌也可以选择跟注或加注，不要过于保守。`;
    prompt += `\n例如：GTO理论下，A2s、K9s等边缘牌在多人底池或有利位置时可以入池。`;
    prompt += `\n\n请以一个真实德州扑克玩家的心态进行思考，展现真实的内心独白和考量过程。考虑牌面强度、位置、对手行为、筹码深度、底池赔率、玩家风格等因素，也可以展现出一些情绪和个性化的心理活动。在分析的基础上，做出你的决策。`;
    prompt += `\n\n请以JSON格式回复，包含action和reason字段：\n{\n  \"action\": \"FOLD\",\n  \"reason\": \"这手牌太弱了，而且我处于不利位置，对手的加注表明他手上有强牌。不值得冒险。\"\n}\n\n或者加注示例:\n{\n  \"action\": \"RAISE\",\n  \"amount\": 100,\n  \"reason\": \"我有两对，感觉非常强势。从对手犹豫的行为来看，我认为他可能只是在试探。加注可以保护我的牌面，也能建立更大的底池。\"\n}`;
    prompt += `\n\n请严格只在以下英文单词中选择你的行动（action），不要输出其它内容：\n- check\n- call\n- raise\n- fold\n- allin\n你的回复必须是有效的JSON对象，action字段只能是上述之一，且必须全为小写或全为大写。`;
    if (actionHistoryText) {
      prompt += `\n\n本局每轮玩家行动历史（含所有阶段）：\n${actionHistoryText}`;
    }
  }
  const phase = gameState.gamePhase;
  const actedArr = Array.isArray(gameState.playersActedByPhase[phase]) ? gameState.playersActedByPhase[phase] : [];
  const actedPlayers = actedArr.map((i: number) => (players[i] && players[i].name) ? players[i].name : `玩家${i}`).join(', ');
  let systemPrompt = '';
  if (player.id === 0) {
    systemPrompt = `你现在是我的德州扑克导师。请用导师身份、第二人称\"你\"给我建议，分析我的手牌、位置、对手行为、筹码深度、底池赔率、玩家风格等，给出详细理由。`;
  } else {
    systemPrompt = `你是一个德州扑克AI玩家。当前游戏设置如下：总人数${playerCount}人，起始筹码${startingChips}，小盲${smallBlind}，大盲${bigBlind}。你当前风格：${player.playStyle || '未知'}（${player.playStyleLevel || 3}星）。请像真实玩家一样思考并决策。特别注意：如果你的手牌很弱、原本想弃牌但可以check时，建议优先check而不是弃牌。但如果你有下注、加注、诈唬或价值下注的理由，请主动行动，不要机械check。请根据真实牌局情况灵活决策。你可以参考GTO（博弈论最优）策略，适当增加入池范围，不要过于保守。`;
  }
  prompt += `\n本轮已决策玩家: ${actedPlayers || '无'}`;
  return { prompt, systemPrompt };
}

// 构造评分prompt
export function buildScorePrompt({ buildLLMPrompt, player, action }: { buildLLMPrompt: any, player: any, action: string }) {
  const { prompt: basePrompt } = buildLLMPrompt(player);
  let prompt = basePrompt.split('请以导师身份')[0] || basePrompt;
  prompt += `\n我刚刚选择了行动：${action}\n请你作为德州扑克导师，对我的这个决策进行1-100分的评分，并详细说明好坏和理由。\n请以JSON格式回复，包含score和reason字段：\n{\n  \"score\": 85,\n  \"reason\": \"你的决策很合理，因为……\"\n}`;
  return prompt;
}

// 解析LLM决策
export function parseLLMDecision(response: string) {
  try {
    const codeBlockMatch = response.match(/```(?:json)?\s*([\s\S]*?)\s*```/i);
    let jsonStr = '';
    if (codeBlockMatch) {
      jsonStr = codeBlockMatch[1];
    } else {
      const match = response.match(/\{[\s\S]*\}/);
      if (match) jsonStr = match[0];
    }
    if (jsonStr) {
      const obj = JSON.parse(jsonStr);
      if (obj.action) {
        return { action: obj.action.toLowerCase(), amount: obj.amount, reason: obj.reason || '' };
      }
    }
  } catch (e) {}
  return { action: 'call', reason: '' };
}

// 解析评分结果
export function parseLLMScore(response: string) {
  try {
    const codeBlockMatch = response.match(/```(?:json)?\s*([\s\S]*?)\s*```/i);
    let jsonStr = '';
    if (codeBlockMatch) {
      jsonStr = codeBlockMatch[1];
    } else {
      const match = response.match(/\{[\s\S]*\}/);
      if (match) jsonStr = match[0];
    }
    if (jsonStr) {
      const obj = JSON.parse(jsonStr);
      if (obj.score !== undefined) {
        return { score: obj.score, reason: obj.reason || '' };
      }
    }
  } catch (e) {}
  return { score: null, reason: '' };
}

// 构建教学分析prompt
export function buildTeachingPrompt(gameState: any, player: any, actionHistory: any) {
  const phaseMap = {
    'preFlop': '翻牌前',
    'flop': '翻牌圈',
    'turn': '转牌圈',
    'river': '河牌圈',
    'showdown': '摊牌'
  };

  // 构建游戏状态描述
  const gameInfo = `
游戏阶段: ${phaseMap[gameState.gamePhase] || gameState.gamePhase}
底池大小: ${gameState.pot}
当前下注: ${gameState.currentBet}
玩家数量: ${gameState.players.length}
`;

  // 构建玩家信息
  const playerInfo = `
玩家位置: ${getPlayerPositionName(gameState.dealerIndex, player.id, gameState.players)}
手牌: ${player.hand.map((c: any) => `${c.suit}${c.value}`).join(', ')}
筹码: ${player.chips}
本局投入: ${player.bet}
`;

  // 构建公共牌信息
  const communityInfo = gameState.communityCards.length > 0 ?
    `公共牌: ${gameState.communityCards.map((c: any) => `${c.suit}${c.value}`).join(', ')}` :
    '公共牌: 暂无';

  // 构建对手信息
  const opponentsInfo = gameState.players
    .filter((p: any) => p.id !== player.id && !p.folded)
    .map((p: any) => `${p.name}: 筹码${p.chips}, 本局投入${p.bet}`)
    .join('\n');

  // 构建行动历史
  const historyText = actionHistoryToText(actionHistory, phaseMap);

  const systemPrompt = `你是一位专业的德州扑克教学专家。请分析当前游戏局面，为玩家提供教学指导。

请返回JSON格式的分析结果，包含以下结构：
{
  "hints": [
    {
      "type": "suggestion|warning|explanation|tip",
      "title": "提示标题",
      "content": "详细的教学内容，要有具体的分析依据",
      "action": "建议的具体行动（可选）",
      "confidence": 85,
      "priority": "high|medium|low",
      "reasoning": "分析的理论依据"
    }
  ],
  "handAnalysis": {
    "strength": 0.75,
    "description": "手牌强度描述",
    "outs": 8,
    "category": "强牌|中等牌|弱牌|听牌"
  },
  "situationAnalysis": {
    "position": "early|middle|late",
    "potOdds": 0.25,
    "impliedOdds": 0.35,
    "stackDepth": "深筹码|中等筹码|短筹码"
  }
}

分析要点：
1. 基于手牌强度和位置给出具体建议
2. 计算准确的底池赔率和隐含赔率
3. 分析对手行为模式和可能的手牌范围
4. 考虑筹码深度对策略的影响
5. 提供有教学价值的理论解释`;

  const userPrompt = `
${gameInfo}
${playerInfo}
${communityInfo}

对手信息:
${opponentsInfo}

行动历史:
${historyText}

请分析当前局面并提供教学指导。`;

  return { prompt: userPrompt, systemPrompt };
}