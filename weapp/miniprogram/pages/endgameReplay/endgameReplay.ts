// 残局复现页面
import { GAME_CONSTANTS } from '../../constants/gameConstants';
import { TeachingManager } from '../../utils/teachingManager';
import type { Player, GameState } from '../../types/gameTypes';

Page({
  data: {
    // 游戏状态
    gameState: {
      deck: [] as { suit: string; value: string }[],
      players: [] as Player[],
      communityCards: [] as { suit: string; value: string }[],
      pot: 0,
      currentBet: 0,
      gamePhase: GAME_CONSTANTS.GAME_PHASES.SETUP,
      currentPlayerIndex: 0,
      dealerIndex: 0
    } as GameState,

    // 设置参数
    playerCount: 3,
    smallBlind: 20,
    bigBlind: 40,
    
    // 手牌设置
    showHandSetting: false,
    currentSettingPlayer: 0,
    selectedCards: [] as { suit: string; value: string }[],
    
    // 公共牌设置
    showCommunityCardSetting: false,
    communityCardIndex: 0,
    
    // 可选卡牌
    availableCards: [] as { suit: string; value: string; selected?: boolean }[],
    
    // 分析结果
    showAnalysis: false,
    analysisResult: null as any,
    
    // 控制状态
    setupComplete: false,
    waitingForAction: false,
    
    // 教学提示
    teachingHints: [] as any[],
    showTeachingPanel: false
  },

  onLoad() {
    this.initializeEndgame();
  },

  /**
   * 初始化残局
   */
  initializeEndgame() {
    // 生成完整牌组
    const availableCards = this.generateFullDeck();
    
    // 初始化玩家
    const players: Player[] = [];
    for (let i = 0; i < this.data.playerCount; i++) {
      players.push({
        id: i,
        name: i === 0 ? '我' : `玩家${i + 1}`,
        chips: 1000,
        bet: 0,
        hand: [],
        folded: false,
        isAI: i !== 0,
        isAllIn: false,
        eliminated: false
      });
    }

    this.setData({
      availableCards,
      'gameState.players': players
    });
  },

  /**
   * 生成完整牌组
   */
  generateFullDeck(): { suit: string; value: string }[] {
    const suits = ['♠', '♥', '♦', '♣'];
    const values = ['2', '3', '4', '5', '6', '7', '8', '9', '10', 'J', 'Q', 'K', 'A'];
    const deck: { suit: string; value: string }[] = [];
    
    suits.forEach(suit => {
      values.forEach(value => {
        deck.push({ suit, value });
      });
    });
    
    return deck;
  },

  /**
   * 设置玩家数量
   */
  onPlayerCountChange(e: any) {
    const playerCount = parseInt(e.detail.value);
    this.setData({ playerCount });
    this.initializeEndgame();
  },

  /**
   * 设置玩家手牌
   */
  setPlayerHand(e: any) {
    const { playerIndex } = e.currentTarget.dataset;
    // 重置所有卡牌的选中状态
    const availableCards = this.data.availableCards.map(c => ({
      ...c,
      selected: false
    }));

    this.setData({
      showHandSetting: true,
      currentSettingPlayer: playerIndex,
      selectedCards: [],
      availableCards
    });
  },

  /**
   * 选择手牌
   */
  selectCard(e: any) {
    const { card } = e.currentTarget.dataset;
    const selectedCards = this.data.selectedCards;
    const availableCards = this.data.availableCards;

    // 检查是否已选择
    const isSelected = selectedCards.some(c =>
      c.suit === card.suit && c.value === card.value
    );

    if (isSelected) {
      // 取消选择
      const newSelected = selectedCards.filter(c =>
        !(c.suit === card.suit && c.value === card.value)
      );
      // 更新可用卡牌的选中状态
      const newAvailableCards = availableCards.map(c => ({
        ...c,
        selected: newSelected.some(sc => sc.suit === c.suit && sc.value === c.value)
      }));

      this.setData({
        selectedCards: newSelected,
        availableCards: newAvailableCards
      });
    } else if (selectedCards.length < 2) {
      // 添加选择（最多2张）
      const newSelected = [...selectedCards, card];
      // 更新可用卡牌的选中状态
      const newAvailableCards = availableCards.map(c => ({
        ...c,
        selected: newSelected.some(sc => sc.suit === c.suit && sc.value === c.value)
      }));

      this.setData({
        selectedCards: newSelected,
        availableCards: newAvailableCards
      });
    }
  },

  /**
   * 确认手牌设置
   */
  confirmHandSetting() {
    if (this.data.selectedCards.length !== 2) {
      wx.showToast({
        title: '请选择2张手牌',
        icon: 'none'
      });
      return;
    }

    const gameState = this.data.gameState;
    const playerIndex = this.data.currentSettingPlayer;
    gameState.players[playerIndex].hand = [...this.data.selectedCards];
    
    // 从可用卡牌中移除已选择的卡牌
    const availableCards = this.data.availableCards.filter(card =>
      !this.data.selectedCards.some(selected =>
        selected.suit === card.suit && selected.value === card.value
      )
    );

    this.setData({
      gameState,
      availableCards,
      showHandSetting: false,
      selectedCards: []
    });
  },

  /**
   * 取消手牌设置
   */
  cancelHandSetting() {
    // 重置所有卡牌的选中状态
    const availableCards = this.data.availableCards.map(c => ({
      ...c,
      selected: false
    }));

    this.setData({
      showHandSetting: false,
      selectedCards: [],
      availableCards
    });
  },

  /**
   * 设置公共牌
   */
  setCommunityCard(e: any) {
    const { cardIndex } = e.currentTarget.dataset;
    // 重置所有卡牌的选中状态
    const availableCards = this.data.availableCards.map(c => ({
      ...c,
      selected: false
    }));

    this.setData({
      showCommunityCardSetting: true,
      communityCardIndex: cardIndex,
      selectedCards: [],
      availableCards
    });
  },

  /**
   * 确认公共牌设置
   */
  confirmCommunityCardSetting() {
    if (this.data.selectedCards.length !== 1) {
      wx.showToast({
        title: '请选择1张公共牌',
        icon: 'none'
      });
      return;
    }

    const gameState = this.data.gameState;
    const cardIndex = this.data.communityCardIndex;
    const selectedCard = this.data.selectedCards[0];
    
    // 确保公共牌数组有足够长度
    while (gameState.communityCards.length <= cardIndex) {
      gameState.communityCards.push({ suit: '', value: '' });
    }
    
    gameState.communityCards[cardIndex] = selectedCard;
    
    // 从可用卡牌中移除
    const availableCards = this.data.availableCards.filter(card =>
      !(card.suit === selectedCard.suit && card.value === selectedCard.value)
    );

    this.setData({
      gameState,
      availableCards,
      showCommunityCardSetting: false,
      selectedCards: []
    });
  },

  /**
   * 取消公共牌设置
   */
  cancelCommunityCardSetting() {
    // 重置所有卡牌的选中状态
    const availableCards = this.data.availableCards.map(c => ({
      ...c,
      selected: false
    }));

    this.setData({
      showCommunityCardSetting: false,
      selectedCards: [],
      availableCards
    });
  },

  /**
   * 开始分析
   */
  startAnalysis() {
    // 验证设置完整性
    if (!this.validateSetup()) {
      return;
    }

    this.setData({
      setupComplete: true,
      waitingForAction: true,
      'gameState.currentPlayerIndex': 0,
      'gameState.gamePhase': GAME_CONSTANTS.GAME_PHASES.FLOP
    });

    // 获取教学分析
    this.getTeachingAnalysis();
  },

  /**
   * 验证设置完整性
   */
  validateSetup(): boolean {
    const gameState = this.data.gameState;
    
    // 检查所有玩家是否都有手牌
    for (let i = 0; i < this.data.playerCount; i++) {
      if (!gameState.players[i].hand || gameState.players[i].hand.length !== 2) {
        wx.showToast({
          title: `请设置玩家${i + 1}的手牌`,
          icon: 'none'
        });
        return false;
      }
    }
    
    // 检查公共牌（至少3张）
    const validCommunityCards = gameState.communityCards.filter(card => 
      card.suit && card.value
    );
    if (validCommunityCards.length < 3) {
      wx.showToast({
        title: '请至少设置3张公共牌',
        icon: 'none'
      });
      return false;
    }
    
    return true;
  },

  /**
   * 获取教学分析
   */
  async getTeachingAnalysis() {
    try {
      const teachingManager = TeachingManager.getInstance();
      const humanPlayer = this.data.gameState.players[0];
      
      // 获取本地分析
      const hints = teachingManager.analyzeGameState(this.data.gameState, humanPlayer);
      
      this.setData({
        teachingHints: hints,
        showTeachingPanel: true
      });
      
      // 这里可以添加AI分析
      this.getAIAnalysis();
      
    } catch (error) {
      console.error('[EndgameReplay] 获取教学分析失败:', error);
      wx.showToast({
        title: '分析失败，请重试',
        icon: 'none'
      });
    }
  },

  /**
   * 获取AI分析
   */
  async getAIAnalysis() {
    // 这里应该调用AI接口进行深度分析
    // 暂时使用模拟数据
    const mockAnalysis = {
      optimalAction: 'raise',
      optimalAmount: 100,
      reasoning: '你的手牌较强，公共牌对你有利，建议加注获取价值。',
      winProbability: 78.5,
      expectedValue: 45.2,
      alternatives: [
        { action: 'call', ev: 32.1, description: '保守跟注，但错失价值' },
        { action: 'fold', ev: -20, description: '弃牌过于保守，浪费强牌' }
      ]
    };

    this.setData({
      analysisResult: mockAnalysis
    });
  },

  /**
   * 执行操作
   */
  performAction(e: any) {
    const { action } = e.currentTarget.dataset;
    
    // 记录玩家操作
    const actualAction = action;
    const analysisResult = this.data.analysisResult;
    
    if (analysisResult) {
      const isOptimal = actualAction === analysisResult.optimalAction;
      
      wx.showModal({
        title: '操作分析',
        content: `你选择了${this.getActionText(actualAction)}。\n\n${
          isOptimal ? 
          '✅ 这是最优选择！' + analysisResult.reasoning :
          '⚠️ 建议选择' + this.getActionText(analysisResult.optimalAction) + '。\n' + analysisResult.reasoning
        }`,
        showCancel: false,
        confirmText: '继续学习'
      });
    }
  },

  /**
   * 获取操作文本
   */
  getActionText(action: string): string {
    const actionMap: { [key: string]: string } = {
      'fold': '弃牌',
      'call': '跟注',
      'raise': '加注',
      'check': '看牌',
      'allin': '全押'
    };
    return actionMap[action] || action;
  },

  /**
   * 重新设置
   */
  resetSetup() {
    this.setData({
      setupComplete: false,
      waitingForAction: false,
      showAnalysis: false,
      analysisResult: null,
      teachingHints: [],
      showTeachingPanel: false
    });
    
    this.initializeEndgame();
  },

  /**
   * 切换教学面板
   */
  toggleTeachingPanel() {
    this.setData({
      showTeachingPanel: !this.data.showTeachingPanel
    });
  },

  /**
   * 页面分享
   */
  onShareAppMessage() {
    return {
      title: '德州扑克残局复现 - 提升决策能力',
      path: '/pages/endgameReplay/endgameReplay',
      imageUrl: '/images/share-endgame-replay.jpg'
    };
  }
});
