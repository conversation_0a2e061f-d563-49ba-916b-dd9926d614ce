/* 统计页面样式 */
.container {
  padding: 20rpx 20rpx calc(40rpx + env(safe-area-inset-bottom)) 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
  box-sizing: border-box;
}

.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400rpx;
  color: #999;
}

/* 用户信息卡片 */
.user-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  color: white;
}

.user-info {
  display: flex;
  align-items: center;
}

.avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  margin-right: 20rpx;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
}

.user-details {
  flex: 1;
}

.nickname {
  font-size: 36rpx;
  font-weight: bold;
  display: block;
  margin-bottom: 10rpx;
}

.level {
  font-size: 28rpx;
  opacity: 0.9;
  display: block;
  margin-bottom: 15rpx;
}

.experience {
  font-size: 24rpx;
}

.exp-bar {
  width: 200rpx;
  height: 8rpx;
  background-color: rgba(255, 255, 255, 0.3);
  border-radius: 4rpx;
  margin-top: 8rpx;
  overflow: hidden;
}

.exp-progress {
  height: 100%;
  background-color: #ffd700;
  border-radius: 4rpx;
  transition: width 0.3s ease;
}

/* 时间周期选择 */
.period-selector {
  display: flex;
  background-color: white;
  border-radius: 15rpx;
  padding: 8rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.period-item {
  flex: 1;
  text-align: center;
  padding: 20rpx;
  border-radius: 10rpx;
  font-size: 28rpx;
  color: #666;
  transition: all 0.3s ease;
}

.period-item.active {
  background-color: #667eea;
  color: white;
}

/* 统计区块 */
.stats-section {
  background-color: white;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.stats-section:last-of-type {
  margin-bottom: 0;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 25rpx;
  padding-bottom: 15rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

/* 核心统计网格 */
.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
}

.stat-item {
  text-align: center;
  padding: 25rpx;
  background-color: #f8f9fa;
  border-radius: 15rpx;
  transition: transform 0.2s ease;
}

.stat-item:active {
  transform: scale(0.95);
}

.stat-value {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.stat-value.positive {
  color: #28a745;
}

.stat-value.negative {
  color: #dc3545;
}

.stat-label {
  font-size: 24rpx;
  color: #666;
}

/* 操作习惯统计 */
.habit-stats {
  space-y: 20rpx;
}

.habit-item {
  display: flex;
  align-items: center;
  margin-bottom: 25rpx;
}

.habit-label {
  width: 200rpx;
  font-size: 26rpx;
  color: #666;
}

.habit-bar {
  flex: 1;
  height: 12rpx;
  background-color: #e9ecef;
  border-radius: 6rpx;
  margin: 0 20rpx;
  overflow: hidden;
}

.habit-progress {
  height: 100%;
  background: linear-gradient(90deg, #28a745, #20c997);
  border-radius: 6rpx;
  transition: width 0.5s ease;
}

.habit-value {
  width: 80rpx;
  text-align: right;
  font-size: 26rpx;
  color: #333;
  font-weight: bold;
}

/* AI统计 */
.ai-stats {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 20rpx;
}

.ai-item {
  text-align: center;
  padding: 20rpx;
  background-color: #f8f9fa;
  border-radius: 12rpx;
}

.ai-label {
  display: block;
  font-size: 24rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.ai-value {
  font-size: 36rpx;
  font-weight: bold;
  color: #667eea;
}

/* 趋势图表 */
.trend-chart {
  margin-top: 20rpx;
}

.chart-container {
  position: relative;
  height: 120rpx;
  background-color: #f8f9fa;
  border-radius: 12rpx;
  margin-bottom: 15rpx;
}

.trend-line {
  position: relative;
  height: 100%;
  padding: 20rpx;
}

.trend-point {
  position: absolute;
  width: 12rpx;
  height: 12rpx;
  border-radius: 50%;
  top: 50%;
  transform: translateY(-50%);
}

.trend-point.positive {
  background-color: #28a745;
}

.trend-point.negative {
  background-color: #dc3545;
}

.trend-labels {
  display: flex;
  justify-content: space-between;
  font-size: 22rpx;
  color: #999;
}

/* 操作按钮 */
.action-buttons {
  display: flex;
  gap: 15rpx;
  margin: 30rpx 0 40rpx 0;
}

.action-btn {
  flex: 1;
  padding: 25rpx;
  border-radius: 15rpx;
  font-size: 28rpx;
  border: none;
}

.action-btn.primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.action-btn.secondary {
  background-color: white;
  color: #667eea;
  border: 2rpx solid #667eea;
}

/* 弹窗样式 */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background-color: white;
  border-radius: 20rpx;
  width: 80%;
  max-width: 600rpx;
  max-height: 80%;
  overflow: hidden;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.modal-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.modal-close {
  font-size: 48rpx;
  color: #999;
  cursor: pointer;
}

.modal-body {
  padding: 30rpx;
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
}

/* 响应式适配 */
@media (max-height: 800rpx) {
  .container {
    padding: 15rpx 15rpx calc(30rpx + env(safe-area-inset-bottom)) 15rpx;
  }

  .stats-section {
    margin-bottom: 15rpx;
    padding: 25rpx;
  }

  .action-buttons {
    margin: 20rpx 0 30rpx 0;
  }
}

@media (max-width: 750rpx) {
  .container {
    padding: 15rpx 15rpx calc(30rpx + env(safe-area-inset-bottom)) 15rpx;
  }

  .user-card {
    padding: 25rpx;
  }

  .stats-grid {
    gap: 15rpx;
  }

  .stat-item {
    padding: 20rpx;
  }

  .action-buttons {
    flex-direction: column;
    gap: 10rpx;
  }
}
