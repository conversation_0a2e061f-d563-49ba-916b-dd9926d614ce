/* 战报详情页面样式 */
.battle-report-detail-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #4CAF50 0%, #2E7D32 100%);
  padding: 20rpx;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 200rpx 0;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
  border-top: 4rpx solid #fff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 24rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  color: #fff;
  font-size: 28rpx;
}

/* 战报内容 */
.report-detail-content {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

/* 头部信息 */
.report-header {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16rpx;
  padding: 32rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.header-info {
  flex: 1;
}

.report-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 8rpx;
}

.report-date {
  font-size: 24rpx;
  color: #666;
  display: block;
}

.header-actions {
  margin-left: 24rpx;
}

.share-btn {
  background: #4CAF50;
  color: #fff;
  border-radius: 50rpx;
  padding: 16rpx 32rpx;
  display: flex;
  align-items: center;
  gap: 8rpx;
  border: none;
  font-size: 26rpx;
}

.share-icon {
  font-size: 24rpx;
}

/* 游戏概览 */
.game-overview {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16rpx;
  padding: 32rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.overview-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 24rpx;
}

.overview-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24rpx;
}

.overview-item {
  text-align: center;
}

.item-label {
  font-size: 22rpx;
  color: #666;
  display: block;
  margin-bottom: 8rpx;
}

.item-value {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  display: block;
}

/* 标签页导航 */
.tab-navigation {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16rpx;
  padding: 8rpx;
  display: flex;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 20rpx;
  border-radius: 12rpx;
  transition: all 0.3s ease;
}

.tab-item.active {
  background: #4CAF50;
}

.tab-text {
  font-size: 26rpx;
  color: #666;
  font-weight: 500;
}

.tab-item.active .tab-text {
  color: #fff;
  font-weight: bold;
}

/* 标签页内容 */
.tab-content {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

/* 通用区块样式 */
.rating-section,
.result-section,
.stats-section,
.analysis-section,
.moments-section,
.summary-section {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16rpx;
  padding: 32rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.section-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 24rpx;
}

/* AI评分 */
.rating-display {
  display: flex;
  align-items: center;
  gap: 32rpx;
}

.rating-circle {
  width: 120rpx;
  height: 120rpx;
  border: 6rpx solid;
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
}

.rating-score {
  font-size: 36rpx;
  font-weight: bold;
  line-height: 1;
}

.rating-total {
  font-size: 20rpx;
  color: #999;
  line-height: 1;
}

.rating-info {
  flex: 1;
}

.rating-level {
  font-size: 32rpx;
  font-weight: bold;
  display: block;
  margin-bottom: 8rpx;
}

.rating-desc {
  font-size: 24rpx;
  color: #666;
  display: block;
}

/* 盈亏结果 */
.result-display {
  text-align: center;
}

.result-amount {
  font-size: 48rpx;
  font-weight: bold;
  display: block;
  margin-bottom: 8rpx;
}

.result-amount.profit {
  color: #4CAF50;
}

.result-amount.loss {
  color: #F44336;
}

.result-unit {
  font-size: 24rpx;
  color: #666;
}

/* 关键数据 */
.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24rpx;
}

.stat-item {
  text-align: center;
  padding: 24rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
}

.stat-value {
  font-size: 32rpx;
  font-weight: bold;
  color: #4CAF50;
  display: block;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 22rpx;
  color: #666;
  display: block;
}

/* 分析列表 */
.analysis-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.analysis-item {
  display: flex;
  align-items: flex-start;
  gap: 16rpx;
  padding: 20rpx;
  border-radius: 12rpx;
}

.analysis-item.strength {
  background: rgba(76, 175, 80, 0.1);
}

.analysis-item.weakness {
  background: rgba(255, 152, 0, 0.1);
}

.analysis-item.recommendation {
  background: rgba(76, 175, 80, 0.1);
}

.analysis-icon {
  font-size: 24rpx;
  line-height: 1.5;
}

.analysis-text {
  flex: 1;
  font-size: 26rpx;
  color: #333;
  line-height: 1.6;
}

/* 关键时刻 */
.moments-list {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.moment-item {
  border: 2rpx solid #e0e0e0;
  border-radius: 12rpx;
  overflow: hidden;
}

.moment-header {
  background: #f8f9fa;
  padding: 20rpx 24rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.moment-title {
  font-size: 26rpx;
  font-weight: bold;
  color: #333;
}

.impact-badge {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 20rpx;
}

.impact-badge.high {
  background: #ffebee;
  color: #f44336;
}

.impact-badge.medium {
  background: #fff3e0;
  color: #ff9800;
}

.impact-badge.low {
  background: #e8f5e8;
  color: #4caf50;
}

.moment-content {
  padding: 24rpx;
}

.moment-description {
  font-size: 26rpx;
  color: #555;
  line-height: 1.6;
  display: block;
  margin-bottom: 20rpx;
}

.action-comparison {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16rpx;
  padding: 16rpx;
  background: #f8f9fa;
  border-radius: 8rpx;
}

.actual-action,
.optimal-action {
  text-align: center;
}

.action-label {
  font-size: 22rpx;
  color: #666;
  display: block;
  margin-bottom: 4rpx;
}

.action-value {
  font-size: 24rpx;
  font-weight: bold;
  color: #333;
  display: block;
}

.moment-analysis {
  font-size: 24rpx;
  color: #666;
  line-height: 1.6;
}

/* 战报摘要 */
.summary-text {
  font-size: 28rpx;
  color: #333;
  line-height: 1.8;
}
