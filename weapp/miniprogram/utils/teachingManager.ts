// AI教学管理器
import type { GameState, Player } from '../types/gameTypes';
import { buildLLMPrompt, buildScorePrompt, parseLLMDecision, parseLLMScore } from './gameUtils';
import { evaluateHand, compareHands, Card } from './pokerHand';
import { GAME_CONSTANTS } from '../constants/gameConstants';

export interface TeachingHint {
  type: 'suggestion' | 'warning' | 'explanation' | 'tip';
  title: string;
  content: string;
  action?: string;
  confidence: number; // 0-100
  priority: 'low' | 'medium' | 'high';
  module?: string; // 所属教学模块
}

export interface TeachingAnalysis {
  situation: string;
  handStrength: string;
  position: string;
  potOdds: number;
  recommendations: string[];
  risks: string[];
  opportunities: string[];
}

export class TeachingManager {
  private static instance: TeachingManager;
  private isTeachingMode: boolean = false;
  private currentHints: TeachingHint[] = [];
  private aiCache: Map<string, { hints: TeachingHint[], timestamp: number }> = new Map();
  private readonly CACHE_DURATION = 30000; // 30秒缓存

  private constructor() {}
  
  public static getInstance(): TeachingManager {
    if (!TeachingManager.instance) {
      TeachingManager.instance = new TeachingManager();
    }
    return TeachingManager.instance;
  }
  
  /**
   * 启用/禁用教学模式
   */
  public setTeachingMode(enabled: boolean): void {
    this.isTeachingMode = enabled;
    if (!enabled) {
      this.currentHints = [];
    }
  }
  
  /**
   * 获取当前教学提示
   */
  public getCurrentHints(): TeachingHint[] {
    return this.currentHints;
  }
  
  /**
   * 分析当前游戏状态并生成教学提示
   */
  public analyzeGameState(gameState: GameState, player: Player): TeachingHint[] {
    if (!this.isTeachingMode) return [];

    // 使用本地详细分析逻辑
    this.currentHints = [];
    this.addBasicHints(gameState, player);
    this.addPositionHints(gameState, player);
    this.addHandStrengthHints(gameState, player);
    this.addPotOddsHints(gameState, player);
    this.addOpponentHints(gameState, player);
    this.addChipManagementHints(gameState, player);

    return this.currentHints;
  }



  /**
   * 生成游戏状态缓存键
   */
  private generateCacheKey(gameState: GameState, player: Player): string {
    const key = `${gameState.gamePhase}_${player.hand.map(c => `${c.suit}${c.value}`).join('')}_${gameState.communityCards.map(c => `${c.suit}${c.value}`).join('')}_${gameState.pot}_${gameState.currentBet}`;
    return key;
  }

  /**
   * 检查缓存
   */
  public checkCache(gameState: GameState, player: Player): TeachingHint[] | null {
    const cacheKey = this.generateCacheKey(gameState, player);
    const cached = this.aiCache.get(cacheKey);

    if (cached && (Date.now() - cached.timestamp) < this.CACHE_DURATION) {
      return cached.hints;
    }

    return null;
  }

  /**
   * 保存到缓存
   */
  public saveToCache(gameState: GameState, player: Player, hints: TeachingHint[]): void {
    const cacheKey = this.generateCacheKey(gameState, player);
    this.aiCache.set(cacheKey, {
      hints,
      timestamp: Date.now()
    });

    // 清理过期缓存
    this.cleanExpiredCache();
  }

  /**
   * 清理过期缓存
   */
  private cleanExpiredCache(): void {
    const now = Date.now();
    for (const [key, value] of this.aiCache.entries()) {
      if (now - value.timestamp > this.CACHE_DURATION) {
        this.aiCache.delete(key);
      }
    }
  }

  /**
   * 获取手牌描述
   */
  private getHandDescription(hand: Card[], communityCards: Card[]): string {
    if (communityCards.length === 0) {
      // 翻牌前
      const [card1, card2] = hand;
      const isSuited = card1.suit === card2.suit;
      const isPair = card1.value === card2.value;

      if (isPair) {
        return `${card1.value}${card1.value}对子`;
      } else {
        return `${card1.value}${card2.value}${isSuited ? '同花' : '非同花'}`;
      }
    } else {
      // 翻牌后，使用手牌评估结果
      const allCards = [...hand, ...communityCards];
      const handEval = evaluateHand(allCards);
      return handEval.name || '未知牌型';
    }
  }

  /**
   * 计算隐含赔率
   */
  private calculateImpliedOdds(gameState: GameState, player: Player, callAmount: number): number {
    // 简化的隐含赔率计算
    const remainingChips = Math.min(...gameState.players.filter(p => !p.folded).map(p => p.chips));
    const potentialWinnings = gameState.pot + callAmount + (remainingChips * 0.3); // 假设能赢得30%的剩余筹码
    return callAmount / potentialWinnings;
  }

  /**
   * 评估玩家决策
   */
  public evaluateDecision(gameState: GameState, player: Player, action: string, amount?: number): { score: number, reason: string } | null {
    const handStrength = this.evaluateHandStrength(player.hand, gameState.communityCards);
    const winProbability = this.estimateWinProbability(player.hand, gameState.communityCards);
    const position = this.getPlayerPosition(gameState, player);
    const callAmount = gameState.currentBet - player.bet;
    const potOdds = callAmount > 0 ? callAmount / (gameState.pot + callAmount) : 0;

    let score = 50; // 基础分数
    let reasons: string[] = [];

    switch (action) {
      case 'fold':
        if (handStrength < 0.3) {
          score += 30;
          reasons.push('弱牌弃牌是正确的保守策略');
        } else if (handStrength > 0.7) {
          score -= 40;
          reasons.push('强牌弃牌可能错失价值');
        } else if (potOdds > winProbability) {
          score += 20;
          reasons.push('底池赔率不利时弃牌合理');
        }
        break;

      case 'call':
        if (winProbability > potOdds) {
          score += 25;
          reasons.push('胜率高于底池赔率，跟注有利');
        } else {
          score -= 20;
          reasons.push('胜率低于底池赔率，跟注不划算');
        }
        if (handStrength > 0.6) {
          score += 15;
          reasons.push('中强牌跟注看后续发展');
        }
        break;

      case 'raise':
        if (handStrength > 0.8) {
          score += 35;
          reasons.push('强牌加注获取价值是最优策略');
        } else if (handStrength < 0.4) {
          score -= 25;
          reasons.push('弱牌加注风险较大');
        }
        if (position === 'late') {
          score += 10;
          reasons.push('后位加注有位置优势');
        } else if (position === 'early') {
          score -= 5;
          reasons.push('前位加注需要更强的手牌');
        }
        break;

      case 'check':
        if (callAmount === 0) {
          if (handStrength < 0.5) {
            score += 20;
            reasons.push('弱牌免费看牌是明智选择');
          } else if (handStrength > 0.7) {
            score -= 15;
            reasons.push('强牌可以考虑下注获取价值');
          }
        }
        break;

      case 'allin':
        if (handStrength > 0.9) {
          score += 40;
          reasons.push('极强牌全押获取最大价值');
        } else if (handStrength < 0.6) {
          score -= 30;
          reasons.push('中等牌全押风险过大');
        }
        const chipsBB = Math.floor(player.chips / (this.getSmallBlindAmount(gameState) * 2));
        if (chipsBB < 10) {
          score += 15;
          reasons.push('短筹码全押是合理策略');
        }
        break;
    }

    // 确保分数在0-100范围内
    score = Math.max(0, Math.min(100, score));

    return {
      score,
      reason: reasons.length > 0 ? reasons.join('；') : '决策基本合理'
    };
  }
  
  /**
   * 获取AI建议（教学版本，更详细）
   */
  public async getTeachingAdvice(gameState: GameState, player: Player): Promise<TeachingAnalysis | null> {
    try {
      const prompt = this.buildTeachingPrompt(gameState, player);
      
      // 这里应该调用AI接口，暂时返回模拟数据
      const analysis = await this.callTeachingAI(prompt);
      
      return analysis;
    } catch (error) {
      console.error('[TeachingManager] 获取AI建议失败:', error);
      return null;
    }
  }
  
  /**
   * 评估玩家决策
   */
  public async evaluatePlayerAction(
    gameState: GameState, 
    player: Player, 
    action: string, 
    amount?: number
  ): Promise<{ score: number; feedback: string; improvements: string[] } | null> {
    try {
      const prompt = this.buildEvaluationPrompt(gameState, player, action, amount);
      
      // 调用AI评估接口
      const evaluation = await this.callEvaluationAI(prompt);
      
      return evaluation;
    } catch (error) {
      console.error('[TeachingManager] 评估决策失败:', error);
      return null;
    }
  }
  
  /**
   * 获取实时提示
   */
  public getRealtimeHint(gameState: GameState, player: Player): TeachingHint | null {
    if (!this.isTeachingMode) return null;
    
    // 根据当前情况生成最重要的提示
    const hints = this.analyzeGameState(gameState, player);
    const highPriorityHints = hints.filter(h => h.priority === 'high');
    
    if (highPriorityHints.length > 0) {
      return highPriorityHints[0];
    }
    
    const mediumPriorityHints = hints.filter(h => h.priority === 'medium');
    if (mediumPriorityHints.length > 0) {
      return mediumPriorityHints[0];
    }
    
    return hints.length > 0 ? hints[0] : null;
  }
  
  /**
   * 获取教学课程列表
   */
  public getTeachingLessons(): Array<{
    id: string;
    title: string;
    description: string;
    difficulty: 'beginner' | 'intermediate' | 'advanced';
    topics: string[];
  }> {
    return [
      {
        id: 'basics',
        title: '德州扑克基础',
        description: '学习基本规则和手牌排名',
        difficulty: 'beginner',
        topics: ['规则介绍', '手牌排名', '基本操作', '位置概念']
      },
      {
        id: 'preflop',
        title: '翻牌前策略',
        description: '掌握起手牌选择和位置策略',
        difficulty: 'intermediate',
        topics: ['起手牌范围', '位置策略', '加注大小', '3-bet策略']
      },
      {
        id: 'postflop',
        title: '翻牌后游戏',
        description: '学习翻牌后的决策和下注',
        difficulty: 'intermediate',
        topics: ['牌面分析', '下注大小', '诈唬技巧', '价值下注']
      },
      {
        id: 'advanced',
        title: '高级策略',
        description: '掌握高级概念和心理战',
        difficulty: 'advanced',
        topics: ['GTO策略', '剥削性打法', '心理战', '资金管理']
      }
    ];
  }
  
  // 私有方法
  private addBasicHints(gameState: GameState, player: Player): void {
    // 根据教学课程模块生成提示
    const lessons = this.getTeachingLessons();
    const currentPhase = gameState.gamePhase;

    // 基础规则模块
    if (currentPhase === GAME_CONSTANTS.GAME_PHASES.PRE_FLOP) {
      this.currentHints.push({
        type: 'tip',
        title: '📚 基础规则 - 翻牌前阶段',
        content: '这是翻牌前阶段，你只能看到自己的两张手牌。要根据手牌强度和位置来决策。',
        confidence: 90,
        priority: 'low',
        module: 'basics'
      });
    }

    // 翻牌前策略模块
    if (currentPhase === GAME_CONSTANTS.GAME_PHASES.PRE_FLOP) {
      this.currentHints.push({
        type: 'explanation',
        title: '🎯 翻牌前策略 - 起手牌选择',
        content: '选择合适的起手牌是成功的关键。强牌如AA、KK应该积极游戏，弱牌应该谨慎。',
        confidence: 85,
        priority: 'medium',
        module: 'preflop'
      });
    }

    // 翻牌后游戏模块
    if ([GAME_CONSTANTS.GAME_PHASES.FLOP, GAME_CONSTANTS.GAME_PHASES.TURN, GAME_CONSTANTS.GAME_PHASES.RIVER].includes(currentPhase)) {
      this.currentHints.push({
        type: 'explanation',
        title: '🃏 翻牌后游戏 - 牌面分析',
        content: '分析公共牌的结构，考虑可能的听牌和成牌。调整你的策略以适应牌面。',
        confidence: 80,
        priority: 'medium',
        module: 'postflop'
      });
    }

    // 操作提示
    const availableActions = this.getAvailableActions(gameState, player);
    if (availableActions.includes('check')) {
      this.currentHints.push({
        type: 'tip',
        title: '💡 基础操作 - 看牌选项',
        content: '当前可以免费看牌(Check)，如果手牌不强，看牌是个不错的选择。',
        confidence: 80,
        priority: 'low',
        module: 'basics'
      });
    }
  }
  
  private addPositionHints(gameState: GameState, player: Player): void {
    const position = this.getPlayerPosition(gameState, player);
    const totalPlayers = gameState.players.filter(p => !p.folded).length;
    const playerIndex = gameState.players.findIndex(p => p.id === player.id);
    const playersAfter = totalPlayers - playerIndex - 1;

    if (position === 'early') {
      this.currentHints.push({
        type: 'warning',
        title: '🎯 位置分析 - 前位劣势',
        content: `你在前位置，后面还有${playersAfter}名玩家要行动。建议只玩前15%的强牌(AA-JJ, AK-AQ)，避免投机性手牌。`,
        confidence: 85,
        priority: 'medium',
        module: 'preflop'
      });
    } else if (position === 'late') {
      this.currentHints.push({
        type: 'suggestion',
        title: '🎯 位置分析 - 后位优势',
        content: `你在后位置，只有${playersAfter}名玩家在你后面。位置优势允许你游戏前30%的手牌，包括小对子和同花连牌。`,
        confidence: 80,
        priority: 'medium',
        module: 'preflop'
      });
    } else {
      this.currentHints.push({
        type: 'explanation',
        title: '🎯 位置分析 - 中位策略',
        content: `你在中位置，后面有${playersAfter}名玩家。建议游戏前20%的手牌，保持相对谨慎的策略。`,
        confidence: 75,
        priority: 'medium',
        module: 'preflop'
      });
    }
  }
  
  private addHandStrengthHints(gameState: GameState, player: Player): void {
    const handStrength = this.evaluateHandStrength(player.hand, gameState.communityCards);
    const winProbability = this.estimateWinProbability(player.hand, gameState.communityCards);
    const handDescription = this.getHandDescription(player.hand, gameState.communityCards);

    if (handStrength > 0.8) {
      this.currentHints.push({
        type: 'suggestion',
        title: '💪 手牌分析 - 强牌价值',
        content: `你的${handDescription}非常强(强度${(handStrength * 100).toFixed(1)}%)，胜率约${(winProbability * 100).toFixed(1)}%。建议积极下注或加注来建立底池，获取最大价值。`,
        action: 'bet/raise',
        confidence: 90,
        priority: 'high',
        module: 'postflop'
      });
    } else if (handStrength > 0.6) {
      this.currentHints.push({
        type: 'suggestion',
        title: '👍 手牌分析 - 中强牌',
        content: `你的${handDescription}中等偏强(强度${(handStrength * 100).toFixed(1)}%)，胜率约${(winProbability * 100).toFixed(1)}%。可以考虑价值下注，但要注意对手的反应。`,
        confidence: 80,
        priority: 'medium',
        module: 'postflop'
      });
    } else if (handStrength < 0.3) {
      this.currentHints.push({
        type: 'warning',
        title: '⚠️ 手牌分析 - 弱牌警告',
        content: `你的${handDescription}较弱(强度${(handStrength * 100).toFixed(1)}%)，胜率仅${(winProbability * 100).toFixed(1)}%。面对下注时要谨慎，考虑弃牌保护筹码。`,
        action: 'fold',
        confidence: 85,
        priority: 'high',
        module: 'postflop'
      });
    } else {
      this.currentHints.push({
        type: 'explanation',
        title: '🤔 手牌分析 - 边缘牌',
        content: `你的${handDescription}中等(强度${(handStrength * 100).toFixed(1)}%)，胜率约${(winProbability * 100).toFixed(1)}%。需要根据位置、对手行为和底池大小来决策。`,
        confidence: 70,
        priority: 'medium',
        module: 'postflop'
      });
    }
  }
  
  private addPotOddsHints(gameState: GameState, player: Player): void {
    const callAmount = gameState.currentBet - player.bet;
    const potSize = gameState.pot;

    if (callAmount > 0 && potSize > 0) {
      const potOdds = callAmount / (potSize + callAmount);
      const winProbability = this.estimateWinProbability(player.hand, gameState.communityCards);
      const impliedOdds = this.calculateImpliedOdds(gameState, player, callAmount);
      const expectedValue = (winProbability * (potSize + callAmount)) - callAmount;

      if (winProbability > potOdds) {
        this.currentHints.push({
          type: 'suggestion',
          title: '📊 底池赔率分析 - 数学有利',
          content: `跟注${callAmount}筹码，底池${potSize}筹码
• 底池赔率: ${(potOdds * 100).toFixed(1)}% (需要胜率)
• 你的胜率: ${(winProbability * 100).toFixed(1)}%
• 隐含赔率: ${(impliedOdds * 100).toFixed(1)}%
• 期望价值: ${expectedValue > 0 ? '+' : ''}${expectedValue.toFixed(0)}筹码

数学上跟注有利可图！`,
          action: 'call',
          confidence: 85,
          priority: 'high',
          module: 'advanced'
        });
      } else {
        this.currentHints.push({
          type: 'warning',
          title: '📊 底池赔率分析 - 数学不利',
          content: `跟注${callAmount}筹码，底池${potSize}筹码
• 底池赔率: ${(potOdds * 100).toFixed(1)}% (需要胜率)
• 你的胜率: ${(winProbability * 100).toFixed(1)}%
• 期望价值: ${expectedValue.toFixed(0)}筹码

数学上跟注不划算，建议弃牌。`,
          action: 'fold',
          confidence: 80,
          priority: 'high',
          module: 'advanced'
        });
      }
    } else if (callAmount === 0) {
      // 可以免费看牌的情况
      this.currentHints.push({
        type: 'tip',
        title: '📊 底池赔率分析 - 免费看牌',
        content: `当前可以免费看牌(Check)，没有额外成本。即使手牌不强，也可以看看下一张牌的情况。`,
        confidence: 90,
        priority: 'low',
        module: 'basics'
      });
    }
  }
  
  private addOpponentHints(gameState: GameState, player: Player): void {
    const activePlayers = gameState.players.filter(p => !p.folded && !p.eliminated);
    const aggressivePlayers = activePlayers.filter(p => p.bet > gameState.currentBet * 0.8);
    
    if (aggressivePlayers.length > 0) {
      this.currentHints.push({
        type: 'warning',
        title: '对手激进',
        content: '有对手表现得很激进，可能持有强牌。要更加谨慎地评估自己的手牌。',
        confidence: 70,
        priority: 'medium'
      });
    }
  }
  
  private addChipManagementHints(gameState: GameState, player: Player): void {
    const smallBlind = this.getSmallBlindAmount(gameState);
    const bigBlind = smallBlind * 2;
    const chipsBB = Math.floor(player.chips / bigBlind); // 以大盲注为单位
    const potBB = Math.floor(gameState.pot / bigBlind);
    const betBB = Math.floor(player.bet / bigBlind);

    if (chipsBB < 10) {
      this.currentHints.push({
        type: 'warning',
        title: '💰 筹码管理 - 短筹码策略',
        content: `你只有${chipsBB}个大盲注(${player.chips}筹码)，属于短筹码。
• 建议采用推拉策略(Push/Fold)
• 只玩前10%的强牌
• 避免小额跟注，要么全押要么弃牌
• 寻找合适时机全押获得筹码`,
        confidence: 90,
        priority: 'high',
        module: 'advanced'
      });
    } else if (chipsBB < 20) {
      this.currentHints.push({
        type: 'warning',
        title: '💰 筹码管理 - 中短筹码',
        content: `你有${chipsBB}个大盲注(${player.chips}筹码)，筹码偏少。
• 避免投机性手牌
• 重点关注位置和手牌强度
• 当前底池${potBB}BB，你已投入${betBB}BB`,
        confidence: 85,
        priority: 'medium',
        module: 'advanced'
      });
    } else if (chipsBB > 50) {
      this.currentHints.push({
        type: 'suggestion',
        title: '💰 筹码管理 - 深筹码优势',
        content: `你有${chipsBB}个大盲注(${player.chips}筹码)，筹码充足。
• 可以游戏更多投机性手牌
• 利用筹码优势给对手施压
• 考虑隐含赔率和后续下注
• 当前底池${potBB}BB，投入${betBB}BB`,
        confidence: 80,
        priority: 'low',
        module: 'advanced'
      });
    } else {
      this.currentHints.push({
        type: 'explanation',
        title: '💰 筹码管理 - 标准筹码',
        content: `你有${chipsBB}个大盲注(${player.chips}筹码)，筹码深度适中。
• 可以进行标准的德州扑克策略
• 当前底池${potBB}BB，你已投入${betBB}BB
• 注意筹码与底池的比例关系`,
        confidence: 75,
        priority: 'low',
        module: 'advanced'
      });
    }
  }
  
  // 辅助方法
  private getAvailableActions(gameState: GameState, player: Player): string[] {
    const actions = [];
    const callAmount = gameState.currentBet - player.bet;
    
    actions.push('fold');
    
    if (callAmount === 0) {
      actions.push('check');
      actions.push('bet');
    } else {
      actions.push('call');
      actions.push('raise');
    }
    
    if (player.chips > 0) {
      actions.push('allin');
    }
    
    return actions;
  }
  
  private getPlayerPosition(gameState: GameState, player: Player): 'early' | 'middle' | 'late' {
    const playerCount = gameState.players.length;
    const relativePosition = (player.id - gameState.dealerIndex + playerCount) % playerCount;
    
    if (relativePosition <= playerCount / 3) return 'early';
    if (relativePosition <= playerCount * 2 / 3) return 'middle';
    return 'late';
  }
  
  private evaluateHandStrength(hand: Card[], communityCards: Card[]): number {
    // 使用真实的手牌评估算法
    if (hand.length < 2) return 0;

    // 如果有公共牌，评估最佳5张牌组合
    if (communityCards.length >= 3) {
      const allCards = [...hand, ...communityCards];
      const handEval = evaluateHand(allCards);

      // 将牌型rank转换为0-1的强度值
      // rank: 9=皇家同花顺, 8=同花顺, 7=四条, 6=葫芦, 5=同花, 4=顺子, 3=三条, 2=两对, 1=一对, 0=高牌
      const baseStrength = handEval.rank / 9; // 基础强度 0-1

      // 根据具体牌值微调强度
      let adjustment = 0;
      if (handEval.value && handEval.value.length > 0) {
        // 高牌值加成（A=14, K=13, Q=12等）
        const highCard = Math.max(...handEval.value);
        adjustment = (highCard - 2) / 120; // 微调范围约0-0.1
      }

      return Math.min(1, baseStrength + adjustment);
    } else {
      // 翻牌前：基于起手牌强度
      return this.evaluatePreFlopStrength(hand);
    }
  }
  
  /**
   * 翻牌前手牌强度评估
   */
  private evaluatePreFlopStrength(hand: Card[]): number {
    if (hand.length !== 2) return 0;

    const [card1, card2] = hand;
    const value1 = this.getCardValue(card1.value);
    const value2 = this.getCardValue(card2.value);
    const isPair = value1 === value2;
    const isSuited = card1.suit === card2.suit;
    const highCard = Math.max(value1, value2);
    const lowCard = Math.min(value1, value2);
    const gap = highCard - lowCard;

    let strength = 0;

    // 对子
    if (isPair) {
      if (value1 >= 14) strength = 0.95; // AA
      else if (value1 >= 13) strength = 0.90; // KK
      else if (value1 >= 12) strength = 0.85; // QQ
      else if (value1 >= 11) strength = 0.80; // JJ
      else if (value1 >= 10) strength = 0.75; // TT
      else if (value1 >= 8) strength = 0.65; // 88-99
      else if (value1 >= 6) strength = 0.55; // 66-77
      else strength = 0.45; // 22-55
    }
    // 同花牌
    else if (isSuited) {
      if (highCard >= 14 && lowCard >= 13) strength = 0.85; // AKs
      else if (highCard >= 14 && lowCard >= 12) strength = 0.80; // AQs
      else if (highCard >= 14 && lowCard >= 11) strength = 0.75; // AJs
      else if (highCard >= 14 && lowCard >= 10) strength = 0.70; // ATs
      else if (highCard >= 13 && lowCard >= 12) strength = 0.75; // KQs
      else if (highCard >= 13 && lowCard >= 11) strength = 0.70; // KJs
      else if (highCard >= 12 && lowCard >= 11) strength = 0.65; // QJs
      else if (gap <= 1 && highCard >= 10) strength = 0.60; // 连牌同花
      else if (gap <= 2 && highCard >= 9) strength = 0.55;
      else if (highCard >= 14) strength = 0.50; // Ax同花
      else strength = 0.35;
    }
    // 非同花牌
    else {
      if (highCard >= 14 && lowCard >= 13) strength = 0.75; // AKo
      else if (highCard >= 14 && lowCard >= 12) strength = 0.70; // AQo
      else if (highCard >= 14 && lowCard >= 11) strength = 0.65; // AJo
      else if (highCard >= 13 && lowCard >= 12) strength = 0.65; // KQo
      else if (highCard >= 13 && lowCard >= 11) strength = 0.60; // KJo
      else if (gap <= 1 && highCard >= 10) strength = 0.50; // 连牌
      else if (highCard >= 14) strength = 0.40; // Ax
      else if (highCard >= 13) strength = 0.35; // Kx
      else strength = 0.25;
    }

    return Math.min(1, Math.max(0, strength));
  }

  /**
   * 获取牌面值的数字表示
   */
  private getCardValue(value: string): number {
    const valueMap: Record<string, number> = {
      '2': 2, '3': 3, '4': 4, '5': 5, '6': 6, '7': 7, '8': 8, '9': 9, '10': 10,
      'J': 11, 'Q': 12, 'K': 13, 'A': 14
    };
    return valueMap[value] || 0;
  }

  private estimateWinProbability(hand: Card[], communityCards: Card[]): number {
    // 基于手牌强度估算胜率
    const handStrength = this.evaluateHandStrength(hand, communityCards);

    // 根据游戏阶段调整胜率估算
    if (communityCards.length === 0) {
      // 翻牌前：直接基于起手牌强度
      return handStrength;
    } else if (communityCards.length === 3) {
      // 翻牌后：考虑改进可能性
      const improvementPotential = this.calculateImprovementPotential(hand, communityCards);
      return Math.min(1, handStrength + improvementPotential * 0.3);
    } else if (communityCards.length === 4) {
      // 转牌后：减少改进可能性
      const improvementPotential = this.calculateImprovementPotential(hand, communityCards);
      return Math.min(1, handStrength + improvementPotential * 0.15);
    } else {
      // 河牌：基于最终牌型
      return handStrength;
    }
  }

  /**
   * 计算改进潜力（简化版outs计算）
   */
  private calculateImprovementPotential(hand: Card[], communityCards: Card[]): number {
    // 这里可以实现更复杂的outs计算
    // 暂时基于手牌和公共牌的配合度给出估算
    const allCards = [...hand, ...communityCards];
    const suits = allCards.map(c => c.suit);
    const values = allCards.map(c => this.getCardValue(c.value));

    let potential = 0;

    // 同花听牌
    const suitCounts = suits.reduce((acc, suit) => {
      acc[suit] = (acc[suit] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const maxSuitCount = Math.max(...Object.values(suitCounts));
    if (maxSuitCount === 4) potential += 0.4; // 同花听牌

    // 顺子听牌（简化判断）
    const sortedValues = [...new Set(values)].sort((a, b) => a - b);
    for (let i = 0; i <= sortedValues.length - 4; i++) {
      const consecutive = sortedValues.slice(i, i + 4);
      if (consecutive[3] - consecutive[0] === 3) {
        potential += 0.3; // 顺子听牌
        break;
      }
    }

    return Math.min(0.5, potential);
  }
  
  private buildTeachingPrompt(gameState: GameState, player: Player): string {
    return `作为德州扑克教练，请分析当前局面并给出详细的教学建议...`;
  }
  
  private buildEvaluationPrompt(gameState: GameState, player: Player, action: string, amount?: number): string {
    return `请评估玩家的这个决策：${action}${amount ? ` ${amount}` : ''}...`;
  }
  
  private async callTeachingAI(prompt: string): Promise<TeachingAnalysis> {
    // 临时返回模拟数据，实际应该调用AI接口
    return {
      situation: '翻牌前中等手牌',
      handStrength: '中等偏上',
      position: '后位置',
      potOdds: 0.25,
      recommendations: ['考虑跟注', '观察对手反应'],
      risks: ['可能遇到更强手牌'],
      opportunities: ['位置优势可以控制底池']
    };
  }
  
  private async callEvaluationAI(prompt: string): Promise<{ score: number; feedback: string; improvements: string[] }> {
    // 临时返回模拟数据
    return {
      score: 75,
      feedback: '这是一个合理的决策，考虑了位置和手牌强度。',
      improvements: ['可以考虑加注来获取更多价值', '注意观察对手的反应']
    };
  }

  /**
   * 获取小盲注金额
   */
  private getSmallBlindAmount(gameState: GameState): number {
    // 从玩家中找到小盲注玩家的下注金额，或使用默认值
    const smallBlindPlayer = gameState.players[gameState.smallBlindIndex];
    if (smallBlindPlayer && smallBlindPlayer.bet > 0) {
      return smallBlindPlayer.bet;
    }
    // 如果找不到，使用默认的小盲注金额
    return 20; // 默认小盲注
  }
}
