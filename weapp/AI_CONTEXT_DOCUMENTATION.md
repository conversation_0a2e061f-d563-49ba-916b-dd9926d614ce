# 德州扑克微信小程序 - AI开发上下文

## 项目概述
基于微信小程序的德州扑克游戏，支持AI玩家对战和鱿鱼模式。使用TypeScript开发

## 核心文件结构
```
weapp/miniprogram/
├── constants/gameConstants.ts   # 游戏常量和配置
├── types/gameTypes.ts          # TypeScript类型定义  
├── utils/
│   ├── gameUtils.ts            # 游戏逻辑工具函数
│   └── pokerHand.ts            # 扑克牌算法
└── pages/game/
    ├── game.ts                 # 游戏主逻辑(2300+行)
    ├── game.wxml               # 游戏界面模板
    └── game.wxss               # 游戏样式(1100+行)
```

## 核心数据结构

### 玩家接口
```typescript
interface Player {
  id: number;
  name: string;
  chips: number;                // 筹码数量
  hand: Card[];                 // 手牌
  bet: number;                  // 当前下注
  folded: boolean;              // 是否弃牌
  isAllIn: boolean;             // 是否全下
  isDealer: boolean;            // 是否庄家
  isSmallBlind: boolean;        // 是否小盲
  isBigBlind: boolean;          // 是否大盲
  isAI: boolean;                // 是否AI玩家
  eliminated: boolean;          // 是否被淘汰
  playStyle?: string;           // AI风格：紧凶/紧弱/松凶/松弱
  hasSquid?: boolean;           // 是否拥有鱿鱼(鱿鱼模式)
}
```

### 游戏状态
```typescript
interface GameState {
  deck: Card[];                 // 牌堆
  players: Player[];            // 玩家列表
  communityCards: Card[];       // 公共牌(最多5张)
  pot: number;                  // 主池金额
  sidePots: SidePot[];         // 边池
  dealerIndex: number;          // 庄家位置
  currentPlayerIndex: number;   // 当前操作玩家
  currentBet: number;           // 当前最高下注
  gamePhase: GamePhase;         // 游戏阶段
  betRoundComplete: boolean;    // 下注轮是否完成
  actionHistory: Record<string, ActionHistoryItem[]>; // 操作历史
}
```

### 鱿鱼模式状态
```typescript
interface SquidGameState {
  enabled: boolean;             // 是否启用鱿鱼模式
  squidHolders: Set<number>;    // 拥有鱿鱼的玩家ID集合
  totalSquids: number;          // 总鱿鱼数量(玩家数-1)
  penaltyAmount: number;        // 惩罚金额
  penaltyMultiplier: number;    // 惩罚倍数(相对大盲注)
}
```

## 游戏常量配置
```typescript
// 来自 constants/gameConstants.ts
export const GAME_CONSTANTS = {
  MIN_PLAYERS: 3,
  MAX_PLAYERS: 10,
  DEFAULT_STARTING_CHIPS: 1000,
  DEFAULT_SMALL_BLIND: 20,
  DEFAULT_BIG_BLIND: 40,
  
  // 游戏阶段
  GAME_PHASES: {
    SETUP: 'setup',
    PRE_FLOP: 'preFlop',
    FLOP: 'flop',
    TURN: 'turn',
    RIVER: 'river',
    SHOWDOWN: 'showdown'
  },
  
  // 玩家操作
  PLAYER_ACTIONS: {
    FOLD: 'fold',
    CHECK: 'check',
    CALL: 'call',
    RAISE: 'raise',
    ALL_IN: 'allin',
    BET: 'bet'
  },
  
  // AI配置
  API: {
    BASE_URL: 'https://shop.hangjw.com/v3/api/ap/chat'
  }
};
```

## 核心功能模块

### 1. 游戏流程控制 (game.ts)
- `startGame()`: 开始游戏
- `startNewHand()`: 开始新一轮
- `dealCards()`: 发牌
- `nextPlayer()`: 切换到下一个玩家
- `endHand()`: 结束当前轮

### 2. 玩家操作处理
- `playerFold()`: 弃牌
- `playerCheck()`: 看牌
- `playerCall()`: 跟注
- `playerRaise()`: 加注
- `playerAllIn()`: 全下

### 3. AI决策系统
- `makeAIDecision()`: AI决策
- `buildLLMPrompt()`: 构建AI提示词
- `parseLLMDecision()`: 解析AI响应

### 4. 鱿鱼模式功能
- `initSquidMode()`: 初始化鱿鱼模式
- `updateSquidStatus()`: 更新鱿鱼状态
- `executeSquidPenalty()`: 执行鱿鱼惩罚

## 重要UI功能

### 筹码动画
```typescript
// 筹码飞行动画数据结构
interface BettingAnimation {
  playerId: number;
  amount: number;
  start: { left: number; top: number };
  end: { left: number; top: number };
}

// 关键方法
addBettingAnimation(playerId, amount, fromPot = false)
```

### 操作气泡
```typescript
// 操作文本映射
const actionTextMap = {
  fold: '弃牌',
  check: '看牌',
  call: (amount) => `跟注${amount}`,
  raise: (amount) => `加注${amount}`,
  allin: '全下'
};

// 显示气泡
showPlayerActionBubble(playerId, action, amount)
```

### 鱿鱼模式UI
- **顶部状态指示器**: 显示 "🦑 2/7" 格式的鱿鱼分配状态
- **玩家鱿鱼徽章**: 拥有鱿鱼的玩家显示绿色发光徽章
- **惩罚提示弹窗**: 触发惩罚时的详细说明

## 关键算法

### 扑克牌评估 (utils/pokerHand.ts)
```typescript
// 牌型强度(1-10)
enum HandRank {
  HIGH_CARD = 1,      // 高牌
  PAIR = 2,           // 对子
  TWO_PAIR = 3,       // 两对
  THREE_KIND = 4,     // 三条
  STRAIGHT = 5,       // 顺子
  FLUSH = 6,          // 同花
  FULL_HOUSE = 7,     // 葫芦
  FOUR_KIND = 8,      // 四条
  STRAIGHT_FLUSH = 9, // 同花顺
  ROYAL_FLUSH = 10    // 皇家同花顺
}

// 核心函数
evaluateHand(cards: Card[]): HandEvaluation
compareHands(hand1: HandEvaluation, hand2: HandEvaluation): number
```

### 边池计算 (utils/gameUtils.ts)
```typescript
// 处理全下玩家产生的边池
function calculateSidePots(allInPlayers: AllInPlayer[]): SidePot[]
function distributePot(winners: Player[], pot: number, sidePots: SidePot[])
```

## 页面数据结构 (game.ts data)
```typescript
data: {
  // 游戏状态
  gameState: GameState,
  
  // 游戏配置
  playerCount: 8,
  startingChips: 1000,
  smallBlind: 20,
  bigBlind: 40,
  
  // 鱿鱼模式
  squidMode: boolean,
  squidHolders: number[],
  squidPenaltyMultiplier: 5,
  
  // UI状态
  showNextRoundBtn: boolean,
  bettingAnimations: BettingAnimation[],
  playerActionBubbles: Record<string, string>,
  
  // AI相关
  assistThinking: boolean,
  aiSuggestion: AISuggestion | null
}
```

## 样式关键类名 (game.wxss)
```css
.container          /* 主容器 */
.table             /* 牌桌 */
.player            /* 玩家区域 */
.card              /* 扑克牌 */
.chips             /* 筹码显示 */
.action-buttons    /* 操作按钮 */
.betting-animation /* 筹码动画 */
.squid-status      /* 鱿鱼状态指示器 */
.squid-badge       /* 鱿鱼徽章 */
```

## 开发注意事项
1. **状态同步**: 所有状态变更通过 `setData()` 更新
2. **动画性能**: 限制同时执行的动画数量
3. **AI超时**: 网络请求设置10秒超时，超时后执行弃牌
4. **鱿鱼模式**: 筹码分配确保守恒原则，支持筹码不足情况
5. **响应式**: 基于750rpx设计，小屏幕自动适配
6. **语法兼容性**: 微信小程序不支持ES2020+语法，避免使用可选链等新特性

## 语法兼容性说明

### 问题背景
微信小程序的JavaScript引擎不支持ES2020的可选链操作符 `?.`，使用会导致预览报错：
```
SyntaxError: Unexpected token .
```

### 兼容性写法对照

#### 1. 简单属性访问
```typescript
// ❌ 不兼容写法
const isAI = player?.isAI;

// ✅ 兼容写法
const isAI = player ? player.isAI : false;
```

#### 2. 嵌套属性访问
```typescript
// ❌ 不兼容写法
const content = res?.data?.data?.content || '';

// ✅ 兼容写法
const content = (res && res.data && res.data.data && res.data.data.content) ? res.data.data.content : '';
```

#### 3. 回调函数调用
```typescript
// ❌ 不兼容写法
callback?.();

// ✅ 兼容写法
callback && callback();
```

#### 4. 复杂对象访问
```typescript
// ❌ 不兼容写法
const phase = this.data?.gameState?.gamePhase || 'unknown';

// ✅ 兼容写法
const phase = (this.data && this.data.gameState && this.data.gameState.gamePhase) ? this.data.gameState.gamePhase : 'unknown';
```

### 其他需要避免的ES2020+语法
- 空值合并操作符 `??`
- 逻辑赋值操作符 `||=`, `&&=`, `??=`
- 数字分隔符 `1_000_000`
- BigInt 字面量 `123n`

### TypeScript配置
项目使用ES5作为编译目标，确保最大兼容性：
```json
{
  "compilerOptions": {
    "target": "ES5",
    "lib": ["ES5", "ES2015.Promise"]
  }
}
```

这份精简文档包含了AI开发所需的核心技术信息，包括语法兼容性要求。
