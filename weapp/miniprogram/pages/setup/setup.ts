Page({
  data: {
    playerCount: 8,
    startingChips: 1000,
    smallBlind: 20,
    bigBlind: 40,
    actionWaitSeconds: 30,
    endHandWaitSeconds: 30,
    teachingMode: false,  // 默认关闭
    teachingModeUnlocked: false,  // 是否已解锁
    squidMode: false,
    squidPenaltyMultiplier: 5,
    battleReportEnabled: true
  },

  onLoad() {
    // 检查AI教学是否在当天内解锁
    this.checkTeachingModeUnlock();
  },

  /**
   * 检查AI教学解锁状态（当天有效）
   */
  checkTeachingModeUnlock() {
    const unlockData = wx.getStorageSync('teachingModeUnlockData');
    const today = new Date().toDateString();

    let teachingModeUnlocked = false;

    if (unlockData && unlockData.date === today) {
      // 当天已解锁
      teachingModeUnlocked = true;
    } else if (unlockData && unlockData.date !== today) {
      // 过期了，清除数据
      wx.removeStorageSync('teachingModeUnlockData');
    }

    this.setData({ teachingModeUnlocked });
  },
  onPlayerCountInput(e: any) {
    this.setData({ playerCount: Number(e.detail.value) });
  },
  onStartingChipsInput(e: any) {
    this.setData({ startingChips: Number(e.detail.value) });
  },
  onSmallBlindInput(e: any) {
    this.setData({ smallBlind: Number(e.detail.value) });
  },
  onBigBlindInput(e: any) {
    this.setData({ bigBlind: Number(e.detail.value) });
  },
  onActionWaitInput(e: any) {
    this.setData({ actionWaitSeconds: Number(e.detail.value) });
  },
  onEndHandWaitInput(e: any) {
    this.setData({ endHandWaitSeconds: Number(e.detail.value) });
  },
  onTeachingModeChange(e: any) {
    if (!this.data.teachingModeUnlocked && e.detail.value) {
      // 如果未解锁且尝试开启，显示解锁提示
      this.showUnlockTeachingMode();
      return;
    }
    this.setData({ teachingMode: e.detail.value });
  },

  /**
   * 显示AI教学解锁提示
   */
  showUnlockTeachingMode() {
    wx.showModal({
      title: '🎓 解锁AI智能教学',
      content: '分享游戏给好友即可解锁AI智能教学功能！\n\n🚀 胜率提升35%，错误决策减少60%\n🧠 AI实时分析，最优策略建议\n⏰ 当天内持续生效，随时可用',
      confirmText: '立即分享',
      cancelText: '稍后再说',
      success: (res) => {
        if (res.confirm) {
          this.shareToUnlockTeaching();
        }
      }
    });
  },

  /**
   * 分享解锁AI教学
   */
  shareToUnlockTeaching() {
    wx.showShareMenu({
      withShareTicket: true,
      success: () => {
        // 模拟分享成功，解锁功能
        setTimeout(() => {
          this.unlockTeachingMode();
        }, 1000);
      }
    });
  },

  /**
   * 解锁AI教学功能（当天有效）
   */
  unlockTeachingMode() {
    const today = new Date().toDateString();
    const unlockData = {
      date: today,
      unlocked: true
    };

    wx.setStorageSync('teachingModeUnlockData', unlockData);
    this.setData({
      teachingModeUnlocked: true,
      teachingMode: true
    });

    wx.showModal({
      title: '🎉 AI教学已解锁！',
      content: '恭喜解锁AI智能教学功能！\n\n🕐 当天内持续生效\n🎯 胜率提升35%\n🧠 智能决策辅助',
      showCancel: false,
      confirmText: '开始体验'
    });
  },
  onSquidModeChange(e: any) {
    this.setData({ squidMode: e.detail.value });
  },
  onSquidPenaltyInput(e: any) {
    this.setData({ squidPenaltyMultiplier: Number(e.detail.value) });
  },
  onBattleReportChange(e: any) {
    this.setData({ battleReportEnabled: e.detail.value });
  },
  onStartGame() {
    const { playerCount, startingChips, smallBlind, bigBlind, actionWaitSeconds, endHandWaitSeconds, teachingMode, squidMode, squidPenaltyMultiplier, battleReportEnabled } = this.data;
    wx.navigateTo({
      url: `/pages/game/game?playerCount=${playerCount}&startingChips=${startingChips}&smallBlind=${smallBlind}&bigBlind=${bigBlind}&actionWaitSeconds=${actionWaitSeconds}&endHandWaitSeconds=${endHandWaitSeconds}&teachingMode=${teachingMode ? 1 : 0}&squidMode=${squidMode ? 1 : 0}&squidPenaltyMultiplier=${squidPenaltyMultiplier}&battleReportEnabled=${battleReportEnabled ? 1 : 0}`
    });
  },

  /**
   * 页面分享
   */
  onShareAppMessage() {
    const { playerCount, startingChips, teachingMode, squidMode, battleReportEnabled } = this.data;
    const features = [];
    if (teachingMode) features.push('🎓教学模式');
    if (squidMode) features.push('🦑鱿鱼模式');
    if (battleReportEnabled) features.push('📊战报分析');

    const featuresText = features.length > 0 ? ` - ${features.join(' ')}` : '';

    return {
      title: `🎮 德州扑克游戏设置 - ${playerCount}人局${featuresText}`,
      path: '/pages/index/index',
      imageUrl: '/images/share-setup.jpg'
    };
  }
});