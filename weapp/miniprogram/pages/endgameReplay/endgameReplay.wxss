/* 残局复现页面样式 */
.endgame-replay-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #4CAF50 0%, #2E7D32 100%);
  padding: 20rpx;
}

/* 页面标题 */
.page-header {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  text-align: center;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.page-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 8rpx;
}

.page-subtitle {
  font-size: 26rpx;
  color: #666;
  display: block;
}

/* 设置阶段 */
.setup-phase {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.setup-section {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16rpx;
  padding: 32rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.section-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 24rpx;
}

/* 基础设置 */
.setting-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 0;
}

.setting-label {
  font-size: 26rpx;
  color: #333;
  font-weight: 500;
}

.picker-display {
  font-size: 26rpx;
  color: #2E7D32;
  font-weight: bold;
  padding: 16rpx 24rpx;
  background: #E8F5E8;
  border-radius: 8rpx;
}

/* 玩家手牌设置 */
.players-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
  margin-top: 16rpx;
}

.player-card {
  border: 2rpx solid #E8F5E8;
  border-radius: 16rpx;
  padding: 24rpx;
  text-align: center;
  transition: all 0.2s ease;
  background: rgba(255, 255, 255, 0.8);
  box-shadow: 0 4rpx 12rpx rgba(76, 175, 80, 0.1);
}

.player-card:active {
  transform: scale(0.95);
  border-color: #4CAF50;
  background: rgba(76, 175, 80, 0.05);
}

.player-name {
  font-size: 26rpx;
  color: #2E7D32;
  font-weight: bold;
  margin-bottom: 16rpx;
}

.player-hand {
  min-height: 80rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.empty-hand {
  color: #81C784;
  font-size: 24rpx;
}

.hand-cards {
  display: flex;
  gap: 12rpx;
  justify-content: center;
}

/* 公共牌设置 */
.community-cards {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  margin-top: 16rpx;
}

.cards-row {
  display: flex;
  align-items: center;
  gap: 20rpx;
  padding: 12rpx 0;
}

.cards-label {
  font-size: 26rpx;
  color: #2E7D32;
  font-weight: bold;
  min-width: 100rpx;
  text-align: right;
}

.cards-group {
  display: flex;
  gap: 16rpx;
  flex: 1;
  justify-content: flex-start;
}

.community-card {
  flex: none;
}

/* 卡牌样式 */
.card {
  width: 90rpx;
  height: 120rpx;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 22rpx;
  font-weight: bold;
  transition: all 0.2s ease;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.card.filled {
  background: #fff;
  border: 2rpx solid #2E7D32;
  color: #2E7D32;
}

.card.empty {
  background: rgba(232, 245, 232, 0.5);
  border: 2rpx dashed #81C784;
  color: #4CAF50;
}

.card:active {
  transform: scale(0.95);
}

.card-text {
  font-size: 18rpx;
  line-height: 1;
}

.empty-text {
  font-size: 24rpx;
  color: #999;
}

/* 开始分析按钮 */
.start-analysis {
  text-align: center;
  margin-top: 24rpx;
}

.analysis-btn {
  background: linear-gradient(135deg, #4CAF50, #45a049);
  color: #fff;
  border-radius: 50rpx;
  padding: 24rpx 48rpx;
  display: inline-flex;
  align-items: center;
  gap: 12rpx;
  border: none;
  font-size: 28rpx;
  font-weight: bold;
  box-shadow: 0 8rpx 24rpx rgba(76, 175, 80, 0.3);
}

.btn-icon {
  font-size: 24rpx;
}

/* 分析阶段 */
.analysis-phase {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.current-situation {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16rpx;
  padding: 32rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.my-hand,
.board-cards {
  display: flex;
  align-items: center;
  gap: 16rpx;
  margin-bottom: 20rpx;
}

.hand-label,
.board-label {
  font-size: 24rpx;
  color: #333;
  font-weight: bold;
  min-width: 120rpx;
}

.board-display {
  display: flex;
  gap: 8rpx;
}

/* 操作选择 */
.action-selection {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16rpx;
  padding: 32rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.action-buttons {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16rpx;
}

.action-btn {
  padding: 24rpx;
  border-radius: 12rpx;
  border: none;
  font-size: 26rpx;
  font-weight: bold;
  transition: all 0.2s ease;
}

.action-btn.fold {
  background: #ffebee;
  color: #f44336;
}

.action-btn.call {
  background: #e3f2fd;
  color: #2196f3;
}

.action-btn.raise {
  background: #e8f5e8;
  color: #4caf50;
}

.action-btn.check {
  background: #fff3e0;
  color: #ff9800;
}

.action-btn:active {
  transform: scale(0.95);
}

/* 分析结果 */
.analysis-result {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16rpx;
  padding: 32rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.optimal-action {
  display: flex;
  align-items: center;
  gap: 12rpx;
  margin-bottom: 16rpx;
  padding: 20rpx;
  background: #e8f5e8;
  border-radius: 12rpx;
}

.optimal-label {
  font-size: 24rpx;
  color: #333;
}

.optimal-value {
  font-size: 28rpx;
  font-weight: bold;
  color: #4caf50;
}

.optimal-amount {
  font-size: 24rpx;
  color: #666;
}

.win-probability {
  display: flex;
  align-items: center;
  gap: 12rpx;
  margin-bottom: 16rpx;
}

.prob-label {
  font-size: 24rpx;
  color: #333;
}

.prob-value {
  font-size: 28rpx;
  font-weight: bold;
  color: #667eea;
}

.reasoning {
  margin-bottom: 24rpx;
}

.reasoning-label {
  font-size: 24rpx;
  color: #333;
  font-weight: bold;
  display: block;
  margin-bottom: 8rpx;
}

.reasoning-text {
  font-size: 26rpx;
  color: #555;
  line-height: 1.6;
}

/* 替代方案 */
.alternatives {
  border-top: 1rpx solid #e0e0e0;
  padding-top: 20rpx;
}

.alternatives-title {
  font-size: 24rpx;
  color: #333;
  font-weight: bold;
  display: block;
  margin-bottom: 16rpx;
}

.alternative-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx;
  background: #f8f9fa;
  border-radius: 8rpx;
  margin-bottom: 12rpx;
}

.alt-action {
  font-size: 24rpx;
  font-weight: bold;
  color: #333;
}

.alt-ev {
  font-size: 22rpx;
  color: #667eea;
}

.alt-desc {
  font-size: 20rpx;
  color: #666;
  flex: 1;
  text-align: right;
}

/* 重新设置按钮 */
.reset-section {
  text-align: center;
}

.reset-btn {
  background: #f5f5f5;
  color: #666;
  border-radius: 50rpx;
  padding: 20rpx 40rpx;
  display: inline-flex;
  align-items: center;
  gap: 8rpx;
  border: none;
  font-size: 26rpx;
}

/* 教学面板 */
.teaching-panel {
  position: fixed;
  right: 20rpx;
  top: 50%;
  transform: translateY(-50%);
  width: 600rpx;
  max-height: 80vh;
  background: rgba(255, 255, 255, 0.98);
  border-radius: 16rpx;
  box-shadow: 0 16rpx 48rpx rgba(0, 0, 0, 0.2);
  z-index: 1000;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 32rpx;
  border-bottom: 1rpx solid #e0e0e0;
}

.panel-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

.panel-close {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f5f5f5;
  border-radius: 50%;
  color: #666;
  font-size: 24rpx;
}

.hints-list {
  max-height: 60vh;
  overflow-y: auto;
  padding: 24rpx;
}

.hint-item {
  margin-bottom: 20rpx;
  padding: 20rpx;
  border-radius: 12rpx;
  border-left: 6rpx solid;
}

.hint-item.high {
  background: rgba(244, 67, 54, 0.1);
  border-left-color: #f44336;
}

.hint-item.medium {
  background: rgba(255, 152, 0, 0.1);
  border-left-color: #ff9800;
}

.hint-item.low {
  background: rgba(76, 175, 80, 0.1);
  border-left-color: #4caf50;
}

.hint-header {
  display: flex;
  align-items: center;
  gap: 12rpx;
  margin-bottom: 8rpx;
}

.hint-type {
  font-size: 20rpx;
}

.hint-title {
  font-size: 24rpx;
  font-weight: bold;
  color: #333;
}

.hint-content {
  font-size: 24rpx;
  color: #555;
  line-height: 1.6;
}

/* 弹窗样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
}

.modal-content {
  width: 90%;
  max-width: 700rpx;
  max-height: 80vh;
  background: #fff;
  border-radius: 16rpx;
  overflow: hidden;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 32rpx;
  background: #f8f9fa;
  border-bottom: 1rpx solid #e0e0e0;
}

.modal-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

.modal-close {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #e0e0e0;
  border-radius: 50%;
  color: #666;
  font-size: 24rpx;
}

.cards-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16rpx;
  padding: 24rpx;
  max-height: 50vh;
  overflow-y: auto;
}

.selectable-card {
  width: 120rpx;
  height: 150rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #fff;
  transition: all 0.2s ease;
}

.selectable-card.selected {
  border-color: #667eea;
  background: #f0f2ff;
}

.selectable-card:active {
  transform: scale(0.95);
}

.modal-actions {
  display: flex;
  gap: 16rpx;
  padding: 24rpx 32rpx;
  background: #f8f9fa;
}

.cancel-btn,
.confirm-btn {
  flex: 1;
  padding: 20rpx;
  border-radius: 12rpx;
  border: none;
  font-size: 26rpx;
  font-weight: bold;
}

.cancel-btn {
  background: #f5f5f5;
  color: #666;
}

.confirm-btn {
  background: #667eea;
  color: #fff;
}
