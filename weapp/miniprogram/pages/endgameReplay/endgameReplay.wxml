<!--残局复现页面-->
<view class="endgame-replay-container">
  <!-- 页面标题 -->
  <view class="page-header">
    <text class="page-title">🎯 残局复现</text>
    <text class="page-subtitle">自定义残局场景，提升决策能力</text>
  </view>

  <!-- 设置阶段 -->
  <view wx:if="{{!setupComplete}}" class="setup-phase">
    <!-- 基础设置 -->
    <view class="setup-section">
      <view class="section-title">⚙️ 基础设置</view>
      <view class="setting-item">
        <text class="setting-label">玩家数量:</text>
        <picker mode="selector" range="{{[2,3,4,5,6,7,8]}}" value="{{playerCount-2}}" bindchange="onPlayerCountChange">
          <view class="picker-display">{{playerCount}}人</view>
        </picker>
      </view>
    </view>

    <!-- 玩家手牌设置 -->
    <view class="setup-section">
      <view class="section-title">🃏 玩家手牌</view>
      <view class="players-grid">
        <view 
          wx:for="{{gameState.players}}" 
          wx:key="id" 
          class="player-card"
          bindtap="setPlayerHand"
          data-player-index="{{index}}"
        >
          <view class="player-name">{{item.name}}</view>
          <view class="player-hand">
            <view wx:if="{{item.hand.length === 0}}" class="empty-hand">
              <text class="empty-text">点击设置手牌</text>
            </view>
            <view wx:else class="hand-cards">
              <view wx:for="{{item.hand}}" wx:key="index" class="card">
                <text class="card-text">{{item.value}}{{item.suit}}</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 公共牌设置 -->
    <view class="setup-section">
      <view class="section-title">🌟 公共牌</view>
      <view class="community-cards">
        <view class="cards-row">
          <text class="cards-label">翻牌:</text>
          <view class="cards-group">
            <view 
              wx:for="{{[0,1,2]}}" 
              wx:key="index" 
              class="community-card"
              bindtap="setCommunityCard"
              data-card-index="{{item}}"
            >
              <view wx:if="{{gameState.communityCards[item] && gameState.communityCards[item].value}}" class="card filled">
                <text class="card-text">{{gameState.communityCards[item].value}}{{gameState.communityCards[item].suit}}</text>
              </view>
              <view wx:else class="card empty">
                <text class="empty-text">+</text>
              </view>
            </view>
          </view>
        </view>
        
        <view class="cards-row">
          <text class="cards-label">转牌:</text>
          <view class="cards-group">
            <view 
              class="community-card"
              bindtap="setCommunityCard"
              data-card-index="3"
            >
              <view wx:if="{{gameState.communityCards[3] && gameState.communityCards[3].value}}" class="card filled">
                <text class="card-text">{{gameState.communityCards[3].value}}{{gameState.communityCards[3].suit}}</text>
              </view>
              <view wx:else class="card empty">
                <text class="empty-text">+</text>
              </view>
            </view>
          </view>
        </view>
        
        <view class="cards-row">
          <text class="cards-label">河牌:</text>
          <view class="cards-group">
            <view 
              class="community-card"
              bindtap="setCommunityCard"
              data-card-index="4"
            >
              <view wx:if="{{gameState.communityCards[4] && gameState.communityCards[4].value}}" class="card filled">
                <text class="card-text">{{gameState.communityCards[4].value}}{{gameState.communityCards[4].suit}}</text>
              </view>
              <view wx:else class="card empty">
                <text class="empty-text">+</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 开始分析按钮 -->
    <view class="start-analysis">
      <button class="analysis-btn" bindtap="startAnalysis">
        <text class="btn-icon">🧠</text>
        <text class="btn-text">开始分析</text>
      </button>
    </view>
  </view>

  <!-- 分析阶段 -->
  <view wx:if="{{setupComplete}}" class="analysis-phase">
    <!-- 当前局面 -->
    <view class="current-situation">
      <view class="section-title">🎮 当前局面</view>
      
      <!-- 我的手牌 -->
      <view class="my-hand">
        <text class="hand-label">我的手牌:</text>
        <view class="hand-cards">
          <view wx:for="{{gameState.players[0].hand}}" wx:key="index" class="card">
            <text class="card-text">{{item.value}}{{item.suit}}</text>
          </view>
        </view>
      </view>
      
      <!-- 公共牌 -->
      <view class="board-cards">
        <text class="board-label">公共牌:</text>
        <view class="board-display">
          <view wx:for="{{gameState.communityCards}}" wx:key="index" class="card">
            <text wx:if="{{item.value}}" class="card-text">{{item.value}}{{item.suit}}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 操作选择 -->
    <view wx:if="{{waitingForAction}}" class="action-selection">
      <view class="section-title">🎯 选择你的操作</view>
      <view class="action-buttons">
        <button class="action-btn fold" bindtap="performAction" data-action="fold">
          <text class="action-text">弃牌</text>
        </button>
        <button class="action-btn call" bindtap="performAction" data-action="call">
          <text class="action-text">跟注</text>
        </button>
        <button class="action-btn raise" bindtap="performAction" data-action="raise">
          <text class="action-text">加注</text>
        </button>
        <button class="action-btn check" bindtap="performAction" data-action="check">
          <text class="action-text">看牌</text>
        </button>
      </view>
    </view>

    <!-- 分析结果 -->
    <view wx:if="{{analysisResult}}" class="analysis-result">
      <view class="section-title">🧠 AI分析结果</view>
      
      <view class="optimal-action">
        <text class="optimal-label">最优操作:</text>
        <text class="optimal-value">{{getActionText(analysisResult.optimalAction)}}</text>
        <text wx:if="{{analysisResult.optimalAmount}}" class="optimal-amount">{{analysisResult.optimalAmount}}筹码</text>
      </view>
      
      <view class="win-probability">
        <text class="prob-label">胜率:</text>
        <text class="prob-value">{{analysisResult.winProbability}}%</text>
      </view>
      
      <view class="reasoning">
        <text class="reasoning-label">分析理由:</text>
        <text class="reasoning-text">{{analysisResult.reasoning}}</text>
      </view>
      
      <!-- 替代方案 -->
      <view wx:if="{{analysisResult.alternatives}}" class="alternatives">
        <text class="alternatives-title">其他选择:</text>
        <view wx:for="{{analysisResult.alternatives}}" wx:key="index" class="alternative-item">
          <text class="alt-action">{{getActionText(item.action)}}</text>
          <text class="alt-ev">期望值: {{item.ev}}</text>
          <text class="alt-desc">{{item.description}}</text>
        </view>
      </view>
    </view>

    <!-- 重新设置按钮 -->
    <view class="reset-section">
      <button class="reset-btn" bindtap="resetSetup">
        <text class="btn-icon">🔄</text>
        <text class="btn-text">重新设置</text>
      </button>
    </view>
  </view>

  <!-- 教学面板 -->
  <view wx:if="{{showTeachingPanel && teachingHints.length > 0}}" class="teaching-panel">
    <view class="panel-header">
      <text class="panel-title">🎓 教学指导</text>
      <view class="panel-close" bindtap="toggleTeachingPanel">✕</view>
    </view>
    <view class="hints-list">
      <view wx:for="{{teachingHints}}" wx:key="index" class="hint-item {{item.priority}}">
        <view class="hint-header">
          <text class="hint-type">{{item.type}}</text>
          <text class="hint-title">{{item.title}}</text>
        </view>
        <text class="hint-content">{{item.content}}</text>
      </view>
    </view>
  </view>
</view>

<!-- 手牌选择弹窗 -->
<view wx:if="{{showHandSetting}}" class="modal-overlay">
  <view class="modal-content">
    <view class="modal-header">
      <text class="modal-title">选择手牌 ({{selectedCards.length}}/2)</text>
      <view class="modal-close" bindtap="cancelHandSetting">✕</view>
    </view>
    <view class="cards-grid">
      <view
        wx:for="{{availableCards}}"
        wx:key="index"
        class="selectable-card {{item.selected ? 'selected' : ''}}"
        bindtap="selectCard"
        data-card="{{item}}"
      >
        <text class="card-text">{{item.value}}{{item.suit}}</text>
      </view>
    </view>
    <view class="modal-actions">
      <button class="cancel-btn" bindtap="cancelHandSetting">取消</button>
      <button class="confirm-btn" bindtap="confirmHandSetting">确认</button>
    </view>
  </view>
</view>

<!-- 公共牌选择弹窗 -->
<view wx:if="{{showCommunityCardSetting}}" class="modal-overlay">
  <view class="modal-content">
    <view class="modal-header">
      <text class="modal-title">选择公共牌</text>
      <view class="modal-close" bindtap="cancelCommunityCardSetting">✕</view>
    </view>
    <view class="cards-grid">
      <view
        wx:for="{{availableCards}}"
        wx:key="index"
        class="selectable-card {{item.selected ? 'selected' : ''}}"
        bindtap="selectCard"
        data-card="{{item}}"
      >
        <text class="card-text">{{item.value}}{{item.suit}}</text>
      </view>
    </view>
    <view class="modal-actions">
      <button class="cancel-btn" bindtap="cancelCommunityCardSetting">取消</button>
      <button class="confirm-btn" bindtap="confirmCommunityCardSetting">确认</button>
    </view>
  </view>
</view>
