// 战报分析管理器
import { DataManager } from './dataManager';
import type { GameState, Player } from '../types/gameTypes';

export interface BattleReport {
  id: string;
  gameId: string;
  timestamp: number;
  gameSettings: {
    playerCount: number;
    startingChips: number;
    smallBlind: number;
    bigBlind: number;
  };
  gameResult: {
    winner: string;
    finalChips: Record<string, number>;
    totalHands: number;
    gameDuration: number;
  };
  playerPerformance: {
    playerId: string;
    playerName: string;
    finalPosition: number;
    chipsWon: number;
    winRate: number;
    aggressionFactor: number;
    vpip: number; // 入池率
    pfr: number;  // 翻牌前加注率
    keyDecisions: KeyDecision[];
  };
  aiAnalysis: {
    overallRating: number; // 0-100
    strengths: string[];
    weaknesses: string[];
    recommendations: string[];
    keyMoments: AnalyzedMoment[];
  };
  summary: string;
  createdAt: number;
}

export interface KeyDecision {
  handNumber: number;
  phase: string;
  action: string;
  amount?: number;
  situation: string;
  result: 'good' | 'neutral' | 'poor';
  reasoning: string;
}

export interface AnalyzedMoment {
  handNumber: number;
  phase: string;
  description: string;
  playerAction: string;
  optimalAction: string;
  impact: 'high' | 'medium' | 'low';
  analysis: string;
}

export class BattleReportManager {
  private static instance: BattleReportManager;
  private dataManager: DataManager;
  private readonly STORAGE_KEY = 'battle_reports';

  private constructor() {
    this.dataManager = DataManager.getInstance();
  }

  public static getInstance(): BattleReportManager {
    if (!BattleReportManager.instance) {
      BattleReportManager.instance = new BattleReportManager();
    }
    return BattleReportManager.instance;
  }

  /**
   * 生成战报
   */
  public async generateBattleReport(
    gameId: string,
    gameState: GameState,
    gameSettings: any,
    gameHistory: any[]
  ): Promise<BattleReport> {
    const humanPlayer = gameState.players.find((p: Player) => !p.isAI);
    if (!humanPlayer) {
      throw new Error('未找到人类玩家');
    }

    // 分析玩家表现
    const playerPerformance = this.analyzePlayerPerformance(humanPlayer, gameHistory, gameSettings, gameState);
    
    // 生成AI分析
    const aiAnalysis = await this.generateAIAnalysis(gameState, gameHistory, humanPlayer);
    
    // 确定获胜者
    const winner = gameState.players.reduce((prev, current) => 
      (current.chips > prev.chips) ? current : prev
    );

    const battleReport: BattleReport = {
      id: this.generateReportId(),
      gameId,
      timestamp: Date.now(),
      gameSettings: {
        playerCount: gameSettings.playerCount || gameState.players.length,
        startingChips: gameSettings.startingChips || 1000,
        smallBlind: gameSettings.smallBlind || 20,
        bigBlind: gameSettings.bigBlind || 40
      },
      gameResult: {
        winner: winner.name,
        finalChips: gameState.players.reduce((acc, p) => {
          acc[p.name] = p.chips;
          return acc;
        }, {} as Record<string, number>),
        totalHands: gameHistory.length,
        gameDuration: this.calculateGameDuration(gameHistory)
      },
      playerPerformance,
      aiAnalysis,
      summary: this.generateSummary(playerPerformance, aiAnalysis),
      createdAt: Date.now()
    };

    // 保存战报
    this.saveBattleReport(battleReport);
    
    return battleReport;
  }

  /**
   * 分析玩家表现
   */
  private analyzePlayerPerformance(
    player: Player,
    gameHistory: any[],
    gameSettings: any,
    gameState: GameState
  ): BattleReport['playerPerformance'] {
    const playerActions = gameHistory.filter(h => h.playerId === player.id);
    const totalActions = playerActions.length;
    
    // 计算各种统计指标
    const vpip = this.calculateVPIP(playerActions, gameHistory);
    const pfr = this.calculatePFR(playerActions, gameHistory);
    const aggressionFactor = this.calculateAggressionFactor(playerActions);
    const winRate = this.calculateWinRate(playerActions);

    // 分析关键决策
    const keyDecisions = this.analyzeKeyDecisions(playerActions);

    // 计算最终排名（基于所有玩家的筹码排序）
    const finalPosition = this.calculateFinalPosition(player, gameState);

    return {
      playerId: player.id.toString(),
      playerName: player.name,
      finalPosition,
      chipsWon: player.chips - gameSettings.startingChips,
      winRate,
      aggressionFactor,
      vpip,
      pfr,
      keyDecisions
    };
  }

  /**
   * 生成AI分析
   */
  private async generateAIAnalysis(
    gameState: GameState,
    gameHistory: any[],
    humanPlayer: Player
  ): Promise<BattleReport['aiAnalysis']> {
    // 这里应该调用AI接口进行分析
    // 暂时返回模拟数据
    const mockAnalysis = {
      overallRating: Math.floor(Math.random() * 40) + 60, // 60-100分
      strengths: [
        '位置意识较好，在后位置能够更积极地游戏',
        '底池赔率计算准确，很少做出数学上不合理的跟注',
        '能够识别对手的下注模式并做出相应调整'
      ],
      weaknesses: [
        '翻牌前过于保守，错失了一些盈利机会',
        '在面对激进对手时容易过度弃牌',
        '价值下注的尺度需要优化，有时下注过小'
      ],
      recommendations: [
        '建议在后位置适当放宽起手牌范围',
        '学习更好地平衡价值下注和诈唬',
        '提高对对手行为模式的观察和记忆'
      ],
      keyMoments: this.analyzeKeyMoments(gameHistory, humanPlayer)
    };

    return mockAnalysis;
  }

  /**
   * 分析关键时刻
   */
  private analyzeKeyMoments(gameHistory: any[], humanPlayer: Player): AnalyzedMoment[] {
    // 找出重要的决策点
    const keyMoments: AnalyzedMoment[] = [];
    
    gameHistory.forEach((hand, index) => {
      if (hand.playerId === humanPlayer.id && hand.amount > 0) {
        keyMoments.push({
          handNumber: index + 1,
          phase: hand.phase || 'unknown',
          description: `第${index + 1}手牌，${hand.phase}阶段`,
          playerAction: `${hand.action}${hand.amount ? ` ${hand.amount}筹码` : ''}`,
          optimalAction: this.getOptimalAction(hand),
          impact: this.getImpactLevel(hand),
          analysis: this.getActionAnalysis(hand)
        });
      }
    });

    return keyMoments.slice(0, 5); // 只返回前5个关键时刻
  }

  /**
   * 计算VPIP（入池率）
   */
  private calculateVPIP(playerActions: any[], gameHistory: any[]): number {
    // 计算总手牌数（以游戏历史中的手牌数为准）
    const totalHands = this.getTotalHandsCount(gameHistory);

    // 计算入池手牌数（玩家主动投入筹码的手牌）
    const handsPlayed = playerActions.filter(a =>
      (a.action === 'call' || a.action === 'raise' || a.action === 'allin') && a.amount > 0
    ).length;

    return totalHands > 0 ? Math.round((handsPlayed / totalHands) * 100) : 0;
  }

  /**
   * 计算PFR（翻牌前加注率）
   */
  private calculatePFR(playerActions: any[], gameHistory: any[]): number {
    // 计算总手牌数
    const totalHands = this.getTotalHandsCount(gameHistory);

    // 计算翻牌前加注的手牌数
    const preflopRaises = playerActions.filter(a =>
      (a.phase === 'preFlop' || a.phase === 'preflop') &&
      (a.action === 'raise' || a.action === 'allin')
    ).length;

    return totalHands > 0 ? Math.round((preflopRaises / totalHands) * 100) : 0;
  }

  /**
   * 计算激进因子
   */
  private calculateAggressionFactor(playerActions: any[]): number {
    const aggressiveActions = playerActions.filter(a => 
      a.action === 'raise' || a.action === 'allin'
    ).length;
    const passiveActions = playerActions.filter(a => 
      a.action === 'call' || a.action === 'check'
    ).length;
    
    return passiveActions > 0 ? 
      Math.round((aggressiveActions / passiveActions) * 100) / 100 : 0;
  }

  /**
   * 计算胜率
   */
  private calculateWinRate(playerActions: any[]): number {
    // 简化计算，实际应该基于手牌结果
    return Math.floor(Math.random() * 30) + 40; // 40-70%
  }

  /**
   * 分析关键决策
   */
  private analyzeKeyDecisions(playerActions: any[]): KeyDecision[] {
    return playerActions
      .filter(action => action.amount > 50) // 只分析大额决策
      .slice(0, 3) // 最多3个关键决策
      .map((action, index) => ({
        handNumber: index + 1,
        phase: action.phase || 'unknown',
        action: action.action,
        amount: action.amount,
        situation: this.describeSituation(action),
        result: this.evaluateDecision(action),
        reasoning: this.getDecisionReasoning(action)
      }));
  }

  /**
   * 获取所有战报
   */
  public getAllBattleReports(): BattleReport[] {
    const reports = this.dataManager.loadData<BattleReport[]>(this.STORAGE_KEY, []);
    return reports.sort((a, b) => b.timestamp - a.timestamp);
  }

  /**
   * 获取单个战报
   */
  public getBattleReport(reportId: string): BattleReport | null {
    const reports = this.getAllBattleReports();
    return reports.find(r => r.id === reportId) || null;
  }

  /**
   * 保存战报
   */
  private saveBattleReport(report: BattleReport): void {
    const reports = this.getAllBattleReports();
    reports.unshift(report);
    
    // 只保留最近50个战报
    const limitedReports = reports.slice(0, 50);
    this.dataManager.saveData(this.STORAGE_KEY, limitedReports);
  }

  // 辅助方法
  private generateReportId(): string {
    return `report_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private calculateGameDuration(gameHistory: any[]): number {
    if (gameHistory.length === 0) return 0;
    const start = gameHistory[0].timestamp || Date.now();
    const end = gameHistory[gameHistory.length - 1].timestamp || Date.now();
    return Math.floor((end - start) / 1000 / 60); // 分钟
  }

  private calculateFinalPosition(player: Player, gameState: GameState): number {
    // 根据最终筹码数量排序计算排名
    const sortedPlayers = gameState.players
      .slice()
      .sort((a, b) => b.chips - a.chips);

    const position = sortedPlayers.findIndex(p => p.id === player.id) + 1;
    return position;
  }

  /**
   * 计算总手牌数
   */
  private getTotalHandsCount(gameHistory: any[]): number {
    // 通过不同的手牌ID或时间戳来计算手牌数
    const handIds = new Set();
    gameHistory.forEach(action => {
      if (action.handId) {
        handIds.add(action.handId);
      } else if (action.timestamp) {
        // 如果没有handId，按时间间隔估算手牌数
        const handIndex = Math.floor(action.timestamp / 60000); // 假设每分钟一手牌
        handIds.add(handIndex);
      }
    });

    // 如果无法准确计算，使用游戏历史长度的估算
    return handIds.size > 0 ? handIds.size : Math.max(1, Math.floor(gameHistory.length / 10));
  }

  private generateSummary(performance: BattleReport['playerPerformance'], analysis: BattleReport['aiAnalysis']): string {
    const position = performance.finalPosition === 1 ? '获得冠军' : `获得第${performance.finalPosition}名`;
    const chipsResult = performance.chipsWon > 0 ? `盈利${performance.chipsWon}筹码` : `亏损${Math.abs(performance.chipsWon)}筹码`;
    
    return `本局游戏${position}，${chipsResult}。整体表现评分${analysis.overallRating}分，${analysis.overallRating >= 80 ? '表现优秀' : analysis.overallRating >= 60 ? '表现良好' : '有待提高'}。`;
  }

  private getOptimalAction(hand: any): string {
    // 简化实现
    const actions = ['fold', 'call', 'raise'];
    return actions[Math.floor(Math.random() * actions.length)];
  }

  private getImpactLevel(hand: any): 'high' | 'medium' | 'low' {
    if (hand.amount > 200) return 'high';
    if (hand.amount > 50) return 'medium';
    return 'low';
  }

  private getActionAnalysis(hand: any): string {
    return `在${hand.phase}阶段选择${hand.action}，这个决策${Math.random() > 0.5 ? '符合' : '偏离'}最优策略。`;
  }

  private describeSituation(action: any): string {
    return `${action.phase}阶段，面对${action.currentBet || 0}筹码的下注`;
  }

  private evaluateDecision(action: any): 'good' | 'neutral' | 'poor' {
    const rand = Math.random();
    if (rand > 0.6) return 'good';
    if (rand > 0.3) return 'neutral';
    return 'poor';
  }

  private getDecisionReasoning(action: any): string {
    const reasons = [
      '基于底池赔率的合理决策',
      '位置优势的有效利用',
      '对手行为模式的正确解读',
      '手牌强度评估准确',
      '风险控制得当'
    ];
    return reasons[Math.floor(Math.random() * reasons.length)];
  }
}
