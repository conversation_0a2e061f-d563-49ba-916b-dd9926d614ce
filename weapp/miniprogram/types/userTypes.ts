// 用户系统相关类型定义

export interface UserProfile {
  id: string;
  nickname: string;
  avatar: string;
  level: number;
  experience: number;
  totalGames: number;
  totalWins: number;
  totalChipsWon: number;
  createdAt: number;
  lastLoginAt: number;
  settings: UserSettings;
  achievements: Achievement[];
}

export interface UserSettings {
  soundEnabled: boolean;
  animationEnabled: boolean;
  autoAction: boolean;
  showAIHints: boolean;
  teachingMode: boolean;
  theme: 'default' | 'dark' | 'classic';
  language: 'zh' | 'en';
}

export interface Achievement {
  id: string;
  name: string;
  description: string;
  icon: string;
  unlockedAt?: number;
  progress?: number;
  maxProgress?: number;
  category: 'game' | 'skill' | 'social' | 'special';
}



export interface UserLevel {
  level: number;
  name: string;
  minExperience: number;
  maxExperience: number;
  benefits: string[];
}

// 用户操作记录
export interface UserAction {
  id: string;
  type: 'login' | 'game_start' | 'game_end' | 'achievement_unlock';
  timestamp: number;
  data?: any;
}

// 签到记录
export interface CheckInRecord {
  date: string; // YYYY-MM-DD
  timestamp: number;
  day: number; // 连续签到天数
  reward: {
    type: 'experience' | 'coins';
    amount: number;
  };
}

// 签到状态
export interface CheckInStatus {
  hasCheckedToday: boolean;
  consecutiveDays: number;
  totalDays: number;
  lastCheckInDate: string | null;
  nextReward: {
    type: 'experience' | 'coins';
    amount: number;
  };
}
