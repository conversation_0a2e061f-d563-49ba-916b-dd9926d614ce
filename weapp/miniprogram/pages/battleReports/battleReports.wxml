<!--战报列表页面-->
<view class="battle-reports-container">
  <!-- 页面标题 -->
  <view class="page-header">
    <view class="header-content">
      <text class="page-title">📊 我的战报</text>
      <text class="page-subtitle">智能分析，助你提升技术</text>
    </view>
  </view>

  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading-container">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载战报中...</text>
  </view>

  <!-- 空状态 -->
  <view wx:elif="{{empty}}" class="empty-container">
    <view class="empty-icon">📋</view>
    <text class="empty-title">暂无战报</text>
    <text class="empty-subtitle">完成游戏后开启战报分析功能即可生成</text>
    <button class="empty-button" bindtap="goHome">开始游戏</button>
  </view>

  <!-- 战报列表 -->
  <view wx:else class="reports-list">
    <view 
      wx:for="{{battleReports}}" 
      wx:key="id" 
      class="report-item"
      bindtap="viewBattleReport"
      data-report-id="{{item.id}}"
    >
      <!-- 战报头部 -->
      <view class="report-header">
        <view class="report-date">
          <text class="date-text">{{formatTime(item.timestamp)}}</text>
          <text class="game-info">{{item.gameSettings.playerCount}}人局 · {{item.gameResult.totalHands}}手牌</text>
        </view>
        <view class="report-actions">
          <view class="action-btn" catchtap="shareBattleReport" data-index="{{index}}">
            <text class="action-icon">📤</text>
          </view>
          <view class="action-btn" catchtap="deleteBattleReport" data-index="{{index}}">
            <text class="action-icon">🗑️</text>
          </view>
        </view>
      </view>

      <!-- 战报内容 -->
      <view class="report-content">
        <!-- 结果概览 -->
        <view class="result-overview">
          <view class="result-item">
            <text class="result-icon">{{getResultIcon(item.playerPerformance.chipsWon)}}</text>
            <view class="result-info">
              <text class="result-label">盈亏</text>
              <text class="result-value {{item.playerPerformance.chipsWon > 0 ? 'profit' : 'loss'}}">
                {{item.playerPerformance.chipsWon > 0 ? '+' : ''}}{{item.playerPerformance.chipsWon}}
              </text>
            </view>
          </view>
          
          <view class="result-item">
            <text class="result-icon">🏆</text>
            <view class="result-info">
              <text class="result-label">排名</text>
              <text class="result-value">第{{item.playerPerformance.finalPosition}}名</text>
            </view>
          </view>
          
          <view class="result-item">
            <text class="result-icon">⭐</text>
            <view class="result-info">
              <text class="result-label">评分</text>
              <text class="result-value rating" style="color: {{getRatingColor(item.aiAnalysis.overallRating)}}">
                {{item.aiAnalysis.overallRating}}分
              </text>
            </view>
          </view>
        </view>

        <!-- 战报摘要 -->
        <view class="report-summary">
          <text class="summary-text">{{item.summary}}</text>
        </view>

        <!-- 关键数据 -->
        <view class="key-stats">
          <view class="stat-item">
            <text class="stat-label">入池率</text>
            <text class="stat-value">{{item.playerPerformance.vpip}}%</text>
          </view>
          <view class="stat-item">
            <text class="stat-label">加注率</text>
            <text class="stat-value">{{item.playerPerformance.pfr}}%</text>
          </view>
          <view class="stat-item">
            <text class="stat-label">激进度</text>
            <text class="stat-value">{{item.playerPerformance.aggressionFactor}}</text>
          </view>
        </view>
      </view>

      <!-- 详细分析内容 -->
      <view class="detail-analysis">
        <!-- AI分析 -->
        <view class="analysis-section">
          <view class="section-title">💪 优势分析</view>
          <view class="analysis-list">
            <view
              wx:for="{{item.aiAnalysis.strengths}}"
              wx:key="index"
              class="analysis-item strength"
            >
              <text class="analysis-icon">✅</text>
              <text class="analysis-text">{{item}}</text>
            </view>
          </view>
        </view>

        <view class="analysis-section">
          <view class="section-title">⚠️ 待改进</view>
          <view class="analysis-list">
            <view
              wx:for="{{item.aiAnalysis.weaknesses}}"
              wx:key="index"
              class="analysis-item weakness"
            >
              <text class="analysis-icon">⚠️</text>
              <text class="analysis-text">{{item}}</text>
            </view>
          </view>
        </view>

        <view class="analysis-section">
          <view class="section-title">💡 改进建议</view>
          <view class="analysis-list">
            <view
              wx:for="{{item.aiAnalysis.recommendations}}"
              wx:key="index"
              class="analysis-item recommendation"
            >
              <text class="analysis-icon">💡</text>
              <text class="analysis-text">{{item}}</text>
            </view>
          </view>
        </view>
      </view>


    </view>
  </view>

  <!-- 底部提示 -->
  <view wx:if="{{!loading && !empty}}" class="bottom-tip">
    <text class="tip-text">💡 战报基于AI分析生成，仅供参考</text>
  </view>
</view>
