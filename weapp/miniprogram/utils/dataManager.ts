// 数据管理工具 - 处理本地存储和数据同步

export class DataManager {
  private static instance: DataManager;
  
  // 存储键名常量
  private static readonly KEYS = {
    USER_PROFILE: 'user_profile',
    GAME_RECORDS: 'game_records',
    STATISTICS: 'statistics',
    ACHIEVEMENTS: 'achievements',

    SETTINGS: 'user_settings',
    CACHE_VERSION: 'cache_version'
  };
  
  private static readonly CURRENT_VERSION = '1.0.0';
  
  private constructor() {
    this.initializeStorage();
  }
  
  public static getInstance(): DataManager {
    if (!DataManager.instance) {
      DataManager.instance = new DataManager();
    }
    return DataManager.instance;
  }
  
  /**
   * 初始化存储，检查版本兼容性
   */
  private initializeStorage(): void {
    try {
      const version = wx.getStorageSync(DataManager.KEYS.CACHE_VERSION);
      if (version !== DataManager.CURRENT_VERSION) {
        console.log('[DataManager] 版本更新，清理旧数据');
        this.clearAllData();
        wx.setStorageSync(DataManager.KEYS.CACHE_VERSION, DataManager.CURRENT_VERSION);
      }
    } catch (error) {
      console.error('[DataManager] 初始化存储失败:', error);
    }
  }
  
  /**
   * 保存数据到本地存储
   */
  public saveData<T>(key: string, data: T): boolean {
    try {
      wx.setStorageSync(key, {
        data,
        timestamp: Date.now(),
        version: DataManager.CURRENT_VERSION
      });
      return true;
    } catch (error) {
      console.error(`[DataManager] 保存数据失败 ${key}:`, error);
      return false;
    }
  }
  
  /**
   * 从本地存储读取数据
   */
  public loadData<T>(key: string, defaultValue?: T): T {
    try {
      const stored = wx.getStorageSync(key);
      if (stored && stored.data !== undefined) {
        return stored.data as T;
      }
      return defaultValue as T;
    } catch (error) {
      console.error(`[DataManager] 读取数据失败 ${key}:`, error);
      return defaultValue as T;
    }
  }
  
  /**
   * 删除指定数据
   */
  public removeData(key: string): boolean {
    try {
      wx.removeStorageSync(key);
      return true;
    } catch (error) {
      console.error(`[DataManager] 删除数据失败 ${key}:`, error);
      return false;
    }
  }
  
  /**
   * 清理所有数据
   */
  public clearAllData(): boolean {
    try {
      wx.clearStorageSync();
      return true;
    } catch (error) {
      console.error('[DataManager] 清理数据失败:', error);
      return false;
    }
  }
  
  /**
   * 获取存储使用情况
   */
  public getStorageInfo(): WechatMiniprogram.GetStorageInfoSyncOption {
    try {
      return wx.getStorageInfoSync();
    } catch (error) {
      console.error('[DataManager] 获取存储信息失败:', error);
      return {
        keys: [],
        currentSize: 0,
        limitSize: 0
      };
    }
  }
  

  

  

  
  // 便捷方法 - 直接使用预定义的键名
  public saveUserProfile(profile: any): boolean {
    return this.saveData(DataManager.KEYS.USER_PROFILE, profile);
  }
  
  public loadUserProfile(): any {
    return this.loadData(DataManager.KEYS.USER_PROFILE, null);
  }
  
  public saveGameRecords(records: any[]): boolean {
    return this.saveData(DataManager.KEYS.GAME_RECORDS, records);
  }
  
  public loadGameRecords(): any[] {
    return this.loadData(DataManager.KEYS.GAME_RECORDS, []);
  }
  
  public saveStatistics(stats: any): boolean {
    return this.saveData(DataManager.KEYS.STATISTICS, stats);
  }
  
  public loadStatistics(): any {
    return this.loadData(DataManager.KEYS.STATISTICS, {});
  }
}
