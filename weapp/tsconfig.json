{"compilerOptions": {"strictNullChecks": true, "noImplicitAny": true, "module": "CommonJS", "target": "ES2017", "allowJs": true, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "experimentalDecorators": true, "noImplicitThis": true, "noImplicitReturns": true, "alwaysStrict": true, "noFallthroughCasesInSwitch": true, "noUnusedLocals": false, "noUnusedParameters": false, "strict": true, "strictPropertyInitialization": false, "lib": ["ES2017", "ES2015.Promise", "ES2015.Core", "ES2015.Collection", "ES2015.Iterable"], "typeRoots": ["./typings"]}, "include": ["./**/*.ts"], "exclude": ["node_modules"]}