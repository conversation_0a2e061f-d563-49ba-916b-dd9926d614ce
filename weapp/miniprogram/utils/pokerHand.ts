// 德州扑克牌型评估与比较
// 牌型rank: 9=皇家同花顺, 8=同花顺, 7=四条, 6=葫芦, 5=同花, 4=顺子, 3=三条, 2=两对, 1=一对, 0=高牌

export interface Card {
  suit: string; // '♠', '♥', '♦', '♣'
  value: string; // '2'-'A'
}

const VALUE_ORDER = ['2','3','4','5','6','7','8','9','10','J','Q','K','A'];
const VALUE_MAP: Record<string, number> = VALUE_ORDER.reduce((acc, v, i) => { acc[v] = i+2; return acc; }, {} as Record<string, number>);

function getCounts(cards: Card[]) {
  const valueCount: Record<string, number> = {};
  const suitCount: Record<string, number> = {};
  for (const c of cards) {
    valueCount[c.value] = (valueCount[c.value]||0)+1;
    suitCount[c.suit] = (suitCount[c.suit]||0)+1;
  }
  return { valueCount, suitCount };
}

function getSortedValues(cards: Card[]) {
  return cards.map(c => VALUE_MAP[c.value]).sort((a,b)=>b-a);
}

function isStraight(values: number[]) {
  const uniq = Array.from(new Set(values)).sort((a,b)=>b-a);
  for (let i=0; i<=uniq.length-5; i++) {
    let ok = true;
    for (let j=1; j<5; j++) if (uniq[i+j] !== uniq[i]-j) ok = false;
    if (ok) return uniq.slice(i,i+5);
  }
  // 特殊A5432顺子
  if (uniq.includes(14) && uniq.includes(2) && uniq.includes(3) && uniq.includes(4) && uniq.includes(5)) {
    return [5,4,3,2,14];
  }
  return null;
}

export function evaluateHand(cards: Card[]): { rank: number, name: string, value: number[] } {
  // 输入7张牌
  const { valueCount, suitCount } = getCounts(cards);
  const values = getSortedValues(cards);
  // 同花
  let flushSuit = null;
  for (const s in suitCount) if (suitCount[s]>=5) flushSuit = s;
  let flushCards = flushSuit ? cards.filter(c=>c.suit===flushSuit).sort((a,b)=>VALUE_MAP[b.value]-VALUE_MAP[a.value]) : [];
  // 同花顺/皇家同花顺
  if (flushCards.length>=5) {
    const flushValues = flushCards.map(c=>VALUE_MAP[c.value]);
    const straight = isStraight(flushValues);
    if (straight) {
      if (straight[0]===14 && straight[1]===13) return { rank:9, name:'皇家同花顺', value:straight };
      return { rank:8, name:'同花顺', value:straight };
    }
  }
  // 四条
  for (const v in valueCount) if (valueCount[v]===4) {
    const kicker = values.filter(val=>val!==VALUE_MAP[v])[0];
    return { rank:7, name:'四条', value:[VALUE_MAP[v],kicker] };
  }
  // 葫芦
  const trips = Object.keys(valueCount).filter(v=>valueCount[v]===3).map(v=>VALUE_MAP[v]).sort((a,b)=>b-a);
  const pairs = Object.keys(valueCount).filter(v=>valueCount[v]===2).map(v=>VALUE_MAP[v]).sort((a,b)=>b-a);
  if (trips.length>=1 && (pairs.length>=1 || trips.length>=2)) {
    const t = trips[0];
    const p = pairs.length>0 ? pairs[0] : trips[1];
    return { rank:6, name:'葫芦', value:[t,p] };
  }
  // 同花
  if (flushCards.length>=5) {
    return { rank:5, name:'同花', value:flushCards.slice(0,5).map(c=>VALUE_MAP[c.value]) };
  }
  // 顺子
  const straight = isStraight(values);
  if (straight) return { rank:4, name:'顺子', value:straight };
  // 三条
  if (trips.length>=1) {
    const kickers = values.filter(v=>v!==trips[0]).slice(0,2);
    return { rank:3, name:'三条', value:[trips[0],...kickers] };
  }
  // 两对
  if (pairs.length>=2) {
    const kicker = values.filter(v=>v!==pairs[0]&&v!==pairs[1])[0];
    return { rank:2, name:'两对', value:[pairs[0],pairs[1],kicker] };
  }
  // 一对
  if (pairs.length>=1) {
    const kickers = values.filter(v=>v!==pairs[0]).slice(0,3);
    return { rank:1, name:'一对', value:[pairs[0],...kickers] };
  }
  // 高牌
  return { rank:0, name:'高牌', value:values.slice(0,5) };
}

export function compareHands(a: {rank:number,value:number[]}, b: {rank:number,value:number[]}): number {
  if (a.rank!==b.rank) return a.rank-b.rank;
  for (let i=0;i<Math.max(a.value.length,b.value.length);i++) {
    if ((a.value[i]||0)!==(b.value[i]||0)) return (a.value[i]||0)-(b.value[i]||0);
  }
  return 0;
} 