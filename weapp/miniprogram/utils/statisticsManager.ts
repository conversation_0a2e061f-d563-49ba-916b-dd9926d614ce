// 统计数据管理器
import type { GameRecord, HandRecord, GameStatistics, TrendData, ActionRecord } from '../types/statisticsTypes';
import type { GameState, Player } from '../types/gameTypes';
import { DataManager } from './dataManager';

export class StatisticsManager {
  private static instance: StatisticsManager;
  private dataManager: DataManager;
  
  private constructor() {
    this.dataManager = DataManager.getInstance();
  }
  
  public static getInstance(): StatisticsManager {
    if (!StatisticsManager.instance) {
      StatisticsManager.instance = new StatisticsManager();
    }
    return StatisticsManager.instance;
  }
  
  /**
   * 记录游戏开始
   */
  public startGameRecord(gameState: GameState, config: any): string {
    const gameId = this.generateGameId();
    const gameRecord: Partial<GameRecord> = {
      id: gameId,
      startTime: Date.now(),
      playerCount: gameState.players.length,
      startingChips: config.startingChips || 1000,
      smallBlind: config.smallBlind || 20,
      bigBlind: config.bigBlind || 40,
      squidMode: config.squidMode || false,
      hands: [],
      aiHintsUsed: 0,
      teachingModeUsed: config.teachingMode || false
    };
    
    // 保存到临时存储
    this.dataManager.saveData(`temp_game_${gameId}`, gameRecord);
    return gameId;
  }
  
  /**
   * 记录游戏结束
   */
  public endGameRecord(gameId: string, gameState: GameState): void {
    const tempRecord = this.dataManager.loadData<Partial<GameRecord>>(`temp_game_${gameId}`);
    if (!tempRecord) return;
    
    const humanPlayer = gameState.players.find(p => !p.isAI);
    if (!humanPlayer) return;
    
    const finalRecord: GameRecord = {
      ...tempRecord,
      endTime: Date.now(),
      duration: Math.floor((Date.now() - tempRecord.startTime!) / 1000),
      playerResult: {
        finalChips: humanPlayer.chips,
        profit: humanPlayer.chips - tempRecord.startingChips!,
        position: this.calculatePlayerPosition(gameState.players, humanPlayer.id),
        handsPlayed: tempRecord.hands?.length || 0,
        handsWon: tempRecord.hands?.filter(h => h.result.won).length || 0,
        totalBet: this.calculateTotalBet(tempRecord.hands || []),
        biggestPot: this.calculateBiggestPot(tempRecord.hands || [])
      }
    } as GameRecord;
    
    // 保存完整记录
    this.saveGameRecord(finalRecord);
    
    // 更新统计数据
    this.updateStatistics(finalRecord);
    
    // 清理临时数据
    this.dataManager.removeData(`temp_game_${gameId}`);
  }
  
  /**
   * 记录手牌开始
   */
  public startHandRecord(gameId: string, handNumber: number, gameState: GameState): void {
    const tempRecord = this.dataManager.loadData<Partial<GameRecord>>(`temp_game_${gameId}`);
    if (!tempRecord) return;
    
    const humanPlayer = gameState.players.find(p => !p.isAI);
    if (!humanPlayer) return;
    
    const handRecord: Partial<HandRecord> = {
      handNumber,
      startTime: Date.now(),
      dealerPosition: gameState.dealerIndex,
      playerPosition: this.getRelativePosition(humanPlayer.id, gameState.dealerIndex, gameState.players.length),
      holeCards: [...humanPlayer.hand],
      communityCards: [],
      actions: []
    };
    
    if (!tempRecord.hands) tempRecord.hands = [];
    tempRecord.hands.push(handRecord as HandRecord);
    
    this.dataManager.saveData(`temp_game_${gameId}`, tempRecord);
  }
  
  /**
   * 记录玩家行动
   */
  public recordAction(gameId: string, action: string, amount: number | undefined, gameState: GameState): void {
    const tempRecord = this.dataManager.loadData<Partial<GameRecord>>(`temp_game_${gameId}`);
    if (!tempRecord || !tempRecord.hands || tempRecord.hands.length === 0) return;
    
    const currentHand = tempRecord.hands[tempRecord.hands.length - 1];
    const humanPlayer = gameState.players.find(p => !p.isAI);
    if (!humanPlayer) return;
    
    const actionRecord: ActionRecord = {
      phase: gameState.gamePhase,
      action,
      amount,
      timestamp: Date.now(),
      position: this.getRelativePosition(humanPlayer.id, gameState.dealerIndex, gameState.players.length)
    };
    
    currentHand.actions.push(actionRecord);
    this.dataManager.saveData(`temp_game_${gameId}`, tempRecord);
  }
  
  /**
   * 记录AI提示使用
   */
  public recordAIHintUsed(gameId: string): void {
    const tempRecord = this.dataManager.loadData<Partial<GameRecord>>(`temp_game_${gameId}`);
    if (!tempRecord) return;
    
    tempRecord.aiHintsUsed = (tempRecord.aiHintsUsed || 0) + 1;
    this.dataManager.saveData(`temp_game_${gameId}`, tempRecord);
  }
  
  /**
   * 完成手牌记录
   */
  public endHandRecord(gameId: string, gameState: GameState, result: { won: boolean; profit: number; potSize: number }): void {
    const tempRecord = this.dataManager.loadData<Partial<GameRecord>>(`temp_game_${gameId}`);
    if (!tempRecord || !tempRecord.hands || tempRecord.hands.length === 0) return;
    
    const currentHand = tempRecord.hands[tempRecord.hands.length - 1];
    const humanPlayer = gameState.players.find(p => !p.isAI);
    if (!humanPlayer) return;
    
    currentHand.endTime = Date.now();
    currentHand.communityCards = [...gameState.communityCards];
    currentHand.result = {
      ...result,
      showdown: gameState.gamePhase === 'showdown'
    };
    
    this.dataManager.saveData(`temp_game_${gameId}`, tempRecord);
  }
  
  /**
   * 获取统计数据
   */
  public getStatistics(): GameStatistics {
    const stats = this.dataManager.loadStatistics();
    if (stats) return stats;
    
    // 返回默认统计数据
    return this.createDefaultStatistics();
  }
  
  /**
   * 获取趋势数据
   */
  public getTrendData(days: number = 30): TrendData[] {
    const records = this.dataManager.loadGameRecords();
    const trends: { [date: string]: TrendData } = {};
    
    const endDate = new Date();
    const startDate = new Date(endDate.getTime() - days * 24 * 60 * 60 * 1000);
    
    // 初始化日期
    for (let d = new Date(startDate); d <= endDate; d.setDate(d.getDate() + 1)) {
      const dateStr = d.toISOString().split('T')[0];
      trends[dateStr] = {
        date: dateStr,
        games: 0,
        wins: 0,
        profit: 0,
        winRate: 0,
        avgScore: 0
      };
    }
    
    // 填充数据
    records.forEach((record: GameRecord) => {
      const date = new Date(record.startTime).toISOString().split('T')[0];
      if (trends[date]) {
        trends[date].games++;
        if (record.playerResult.profit > 0) trends[date].wins++;
        trends[date].profit += record.playerResult.profit;
      }
    });
    
    // 计算比率
    Object.values(trends).forEach(trend => {
      if (trend.games > 0) {
        trend.winRate = trend.wins / trend.games;
        trend.avgScore = trend.profit / trend.games;
      }
    });
    
    return Object.values(trends).sort((a, b) => a.date.localeCompare(b.date));
  }
  
  // 私有辅助方法
  private generateGameId(): string {
    return `game_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
  
  private calculatePlayerPosition(players: Player[], playerId: number): number {
    const sortedPlayers = [...players].sort((a, b) => b.chips - a.chips);
    return sortedPlayers.findIndex(p => p.id === playerId) + 1;
  }
  
  private getRelativePosition(playerId: number, dealerIndex: number, playerCount: number): number {
    return (playerId - dealerIndex + playerCount) % playerCount;
  }
  
  private calculateTotalBet(hands: HandRecord[]): number {
    return hands.reduce((total, hand) => {
      return total + hand.actions.reduce((handTotal, action) => {
        return handTotal + (action.amount || 0);
      }, 0);
    }, 0);
  }
  
  private calculateBiggestPot(hands: HandRecord[]): number {
    return Math.max(...hands.map(hand => hand.result.potSize), 0);
  }
  
  private saveGameRecord(record: GameRecord): void {
    const records = this.dataManager.loadGameRecords();
    records.push(record);
    
    // 只保留最近100局游戏
    if (records.length > 100) {
      records.splice(0, records.length - 100);
    }
    
    this.dataManager.saveGameRecords(records);
  }
  
  private updateStatistics(record: GameRecord): void {
    const stats = this.getStatistics();
    
    // 更新基础统计
    stats.totalGames++;
    stats.totalHands += record.hands.length;
    if (record.playerResult.profit > 0) stats.totalWins++;
    stats.totalProfit += record.playerResult.profit;
    stats.totalPlayTime += record.duration;
    
    // 重新计算比率
    stats.winRate = stats.totalWins / stats.totalGames;
    stats.handWinRate = record.playerResult.handsWon / Math.max(record.playerResult.handsPlayed, 1);
    stats.avgProfit = stats.totalProfit / stats.totalGames;
    
    // 更新最大值
    if (record.playerResult.profit > stats.biggestWin) {
      stats.biggestWin = record.playerResult.profit;
    }
    if (record.playerResult.profit < stats.biggestLoss) {
      stats.biggestLoss = record.playerResult.profit;
    }
    
    // 更新AI统计
    if (!stats.aiStats) {
      stats.aiStats = {
        hintsUsed: 0,
        hintsFollowed: 0,
        teachingModeGames: 0,
        avgAIScore: 0
      };
    }
    stats.aiStats.hintsUsed += record.aiHintsUsed || 0;
    if (record.teachingModeUsed) {
      stats.aiStats.teachingModeGames++;
    }
    
    this.dataManager.saveStatistics(stats);
  }
  
  private createDefaultStatistics(): GameStatistics {
    return {
      totalGames: 0,
      totalHands: 0,
      totalWins: 0,
      totalProfit: 0,
      totalPlayTime: 0,
      winRate: 0,
      handWinRate: 0,
      showdownWinRate: 0,
      avgProfit: 0,
      biggestWin: 0,
      biggestLoss: 0,
      profitTrend: [],
      vpip: 0,
      pfr: 0,
      aggression: 0,
      foldToRaise: 0,
      positionStats: {},
      phaseStats: {},
      aiStats: {
        hintsUsed: 0,
        hintsFollowed: 0,
        teachingModeGames: 0,
        avgAIScore: 0
      }
    };
  }
}
