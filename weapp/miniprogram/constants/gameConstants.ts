// 游戏常量定义
export const GAME_CONSTANTS = {
  // 游戏配置
  MIN_PLAYERS: 3,
  MAX_PLAYERS: 10,
  MIN_CHIPS: 100,
  DEFAULT_STARTING_CHIPS: 1000,
  DEFAULT_SMALL_BLIND: 20,
  DEFAULT_BIG_BLIND: 40,
  
  // 时间配置
  ACTION_TIMEOUT: 30000, // 30秒
  END_HAND_COUNTDOWN: 30000, // 30秒
  ANIMATION_DELAY: 60, // 60ms
  NEXT_ROUND_DELAY: 800, // 800ms
  CHIP_ANIMATION_DELAY: 18, // 18ms
  
  // 网络配置
  REQUEST_TIMEOUT: 10000, // 10秒
  MAX_RETRY_COUNT: 3,
  
  // 动画配置
  CHIP_DENOMINATIONS: [1000, 500, 200, 100, 50, 25, 5],
  
  // 游戏阶段
  GAME_PHASES: {
    SETUP: 'setup',
    PRE_FLOP: 'preFlop',
    FLOP: 'flop',
    TURN: 'turn',
    RIVER: 'river',
    SHOWDOWN: 'showdown'
  } as const,
  
  // 玩家操作
  PLAYER_ACTIONS: {
    FOLD: 'fold',
    CHECK: 'check',
    CALL: 'call',
    RAISE: 'raise',
    ALL_IN: 'allin',
    SMALL_BLIND: 'smallBlind',
    BIG_BLIND: 'bigBlind',
    BET: 'bet'
  } as const,
  
  // 玩家风格
  PLAY_STYLES: ['紧凶', '紧弱', '松凶', '松弱'] as const,
  
  // API配置
  API: {
    BASE_URL: 'https://shop.hangjw.com/v3/api/ap/chat',
    ENDPOINTS: {
      CHAT: '/chat'
    }
  }
};

// 阶段映射表（中文）
export const PHASE_MAP: Record<string, string> = {
  [GAME_CONSTANTS.GAME_PHASES.SETUP]: '准备阶段',
  [GAME_CONSTANTS.GAME_PHASES.PRE_FLOP]: '翻牌前',
  [GAME_CONSTANTS.GAME_PHASES.FLOP]: '翻牌圈',
  [GAME_CONSTANTS.GAME_PHASES.TURN]: '转牌圈',
  [GAME_CONSTANTS.GAME_PHASES.RIVER]: '河牌圈',
  [GAME_CONSTANTS.GAME_PHASES.SHOWDOWN]: '摊牌结算'
};

// AI模型配置
export const AI_MODEL_CONFIGS = [
  { model: 'tongyi', version: 'qwen-max', proxy: false },
  // 可以根据需要添加更多模型
];

// 扑克牌常量
export const POKER_CONSTANTS = {
  SUITS: ['♠', '♥', '♦', '♣'] as const,
  VALUES: ['2', '3', '4', '5', '6', '7', '8', '9', '10', 'J', 'Q', 'K', 'A'] as const
};
