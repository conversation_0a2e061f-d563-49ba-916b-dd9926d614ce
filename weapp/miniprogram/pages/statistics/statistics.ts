// 统计页面
import { StatisticsManager } from '../../utils/statisticsManager';
import { UserManager } from '../../utils/userManager';
import type { GameStatistics, TrendData } from '../../types/statisticsTypes';

Page({
  data: {
    statistics: {} as GameStatistics,
    trendData: [] as TrendData[],
    selectedPeriod: 'week' as 'week' | 'month' | 'all',
    loading: true,

    // 显示控制
    showDetailModal: false,
    selectedStat: '',

    // 用户信息
    userProfile: null as any,
    userLevel: null as any,

    // 格式化显示数据
    winRateDisplay: '0.0',
    avgProfitDisplay: '0',
    avgProfitClass: '',
    playTimeDisplay: '0',
    vpipDisplay: '0.0',
    vpipWidth: 0,
    pfrDisplay: '0.0',
    pfrWidth: 0,
    aggressionDisplay: '0.0',
    aggressionWidth: 0,
    avgAIScoreDisplay: '0.0',
    trendPointPosition: [] as number[],
  },

  onLoad() {
    this.loadData();
  },

  onShow() {
    // 每次显示页面时刷新数据
    this.loadData();
  },

  /**
   * 下拉刷新
   */
  onPullDownRefresh() {
    this.loadData();
    // 停止下拉刷新
    setTimeout(() => {
      wx.stopPullDownRefresh();
    }, 1000);
  },

  /**
   * 加载统计数据
   */
  loadData() {
    this.setData({ loading: true });
    
    try {
      // 获取统计数据
      const statsManager = StatisticsManager.getInstance();
      const statistics = statsManager.getStatistics();
      
      // 获取趋势数据
      const days = this.data.selectedPeriod === 'week' ? 7 : 
                   this.data.selectedPeriod === 'month' ? 30 : 90;
      const trendData = statsManager.getTrendData(days);
      
      // 获取用户信息
      const userManager = UserManager.getInstance();
      const userProfile = userManager.getCurrentUser();
      const userLevel = userManager.getUserLevel();
      
      // 格式化显示数据
      const formattedData = this.formatDisplayData(statistics, trendData);

      this.setData({
        statistics,
        trendData,
        userProfile,
        userLevel,
        loading: false,
        ...formattedData
      });

      console.log('[Statistics] 数据加载完成:', {
        totalGames: statistics.totalGames,
        trendDataLength: trendData.length,
        userLevel: userLevel?.level
      });
      
    } catch (error) {
      console.error('[Statistics] 加载数据失败:', error);
      this.setData({ loading: false });
      wx.showToast({
        title: '加载失败',
        icon: 'error'
      });
    }
  },

  /**
   * 格式化显示数据
   */
  formatDisplayData(statistics: GameStatistics, trendData: TrendData[]) {
    // 胜率显示
    const winRateDisplay = statistics.winRate ? (statistics.winRate * 100).toFixed(1) : '0.0';

    // 平均盈利显示
    const avgProfit = statistics.avgProfit || 0;
    const avgProfitDisplay = avgProfit >= 0 ? `+${avgProfit.toFixed(0)}` : avgProfit.toFixed(0);
    const avgProfitClass = avgProfit >= 0 ? 'positive' : 'negative';

    // 游戏时长显示
    const playTimeDisplay = statistics.totalPlayTime ? Math.floor(statistics.totalPlayTime / 3600).toString() : '0';

    // 操作习惯显示
    const vpip = statistics.vpip || 0;
    const vpipDisplay = (vpip * 100).toFixed(1);
    const vpipWidth = vpip * 100;

    const pfr = statistics.pfr || 0;
    const pfrDisplay = (pfr * 100).toFixed(1);
    const pfrWidth = pfr * 100;

    const aggression = statistics.aggression || 0;
    const aggressionDisplay = (aggression * 100).toFixed(1);
    const aggressionWidth = aggression * 100;

    // AI评分显示
    const avgAIScoreDisplay = statistics.aiStats && statistics.aiStats.avgAIScore ?
      statistics.aiStats.avgAIScore.toFixed(1) : '0.0';

    // 趋势点位置计算
    const trendPointPosition = trendData.map((_, index) => {
      return trendData.length > 1 ? (index / (trendData.length - 1)) * 100 : 50;
    });

    return {
      winRateDisplay,
      avgProfitDisplay,
      avgProfitClass,
      playTimeDisplay,
      vpipDisplay,
      vpipWidth,
      pfrDisplay,
      pfrWidth,
      aggressionDisplay,
      aggressionWidth,
      avgAIScoreDisplay,
      trendPointPosition
    };
  },

  /**
   * 切换时间周期
   */
  onPeriodChange(e: any) {
    const period = e.currentTarget.dataset.period;
    this.setData({ selectedPeriod: period });
    this.loadData();
  },

  /**
   * 显示详细信息
   */
  showStatDetail(e: any) {
    const stat = e.currentTarget.dataset.stat;
    this.setData({
      selectedStat: stat,
      showDetailModal: true
    });
  },

  /**
   * 关闭详细信息弹窗
   */
  closeDetailModal() {
    this.setData({ showDetailModal: false });
  },

  /**
   * 获取统计项的详细说明
   */
  getStatDescription(stat: string): string {
    const descriptions: { [key: string]: string } = {
      'winRate': '游戏胜率 = 获胜局数 / 总游戏局数',
      'handWinRate': '手牌胜率 = 获胜手牌数 / 总手牌数',
      'avgProfit': '平均盈利 = 总盈利 / 总游戏局数',
      'vpip': '入池率 = 主动投入底池的手牌比例',
      'pfr': '翻牌前加注率 = 翻牌前加注的手牌比例',
      'aggression': '激进度 = (下注+加注) / (下注+加注+跟注)',
      'foldToRaise': '面对加注弃牌率 = 面对加注时弃牌的比例'
    };
    return descriptions[stat] || '暂无说明';
  },

  /**
   * 格式化数字显示
   */
  formatNumber(num: number, type: 'percentage' | 'chips' | 'time' | 'count' = 'count'): string {
    if (typeof num !== 'number' || isNaN(num)) return '0';
    
    switch (type) {
      case 'percentage':
        return `${(num * 100).toFixed(1)}%`;
      case 'chips':
        return num >= 0 ? `+${num}` : `${num}`;
      case 'time':
        const hours = Math.floor(num / 3600);
        const minutes = Math.floor((num % 3600) / 60);
        return hours > 0 ? `${hours}h ${minutes}m` : `${minutes}m`;
      default:
        return num.toString();
    }
  },

  /**
   * 获取趋势指示器
   */
  getTrendIndicator(current: number, previous: number): string {
    if (previous === 0) return '';
    const change = ((current - previous) / previous) * 100;
    if (change > 5) return '📈';
    if (change < -5) return '📉';
    return '➡️';
  },

  /**
   * 跳转到游戏页面
   */
  goToGame() {
    wx.navigateTo({
      url: '/pages/setup/setup'
    });
  },

  /**
   * 跳转到用户资料页面
   */
  goToProfile() {
    wx.navigateTo({
      url: '/pages/profile/profile'
    });
  },

  /**
   * 分享统计数据
   */
  shareStats() {
    const { statistics } = this.data;
    const winRate = this.formatNumber(statistics.winRate, 'percentage');
    const totalGames = statistics.totalGames;
    const avgProfit = this.formatNumber(statistics.avgProfit, 'chips');
    
    wx.showShareMenu({
      withShareTicket: true,
      menus: ['shareAppMessage', 'shareTimeline']
    });
    
    // 可以在这里自定义分享内容
    console.log(`我的德州扑克战绩：胜率${winRate}，共${totalGames}局，平均盈利${avgProfit}`);
  },

  /**
   * 重置统计数据
   */
  resetStats() {
    wx.showModal({
      title: '确认重置',
      content: '重置后所有统计数据将被清空，此操作不可恢复',
      success: (res) => {
        if (res.confirm) {
          try {
            // 这里可以添加重置逻辑
            wx.showToast({
              title: '重置成功',
              icon: 'success'
            });
            this.loadData();
          } catch (error) {
            wx.showToast({
              title: '重置失败',
              icon: 'error'
            });
          }
        }
      }
    });
  },

  /**
   * 页面分享
   */
  onShareAppMessage() {
    const stats = this.data.statistics;
    const winRate = stats.winRate ? (stats.winRate * 100).toFixed(1) : '0.0';
    const totalProfit = stats.totalChipsWon || 0;
    const profitText = totalProfit > 0 ? `盈利${totalProfit}筹码` : totalProfit < 0 ? `亏损${Math.abs(totalProfit)}筹码` : '盈亏平衡';

    return {
      title: `📊 我的德州扑克数据 - 胜率${winRate}% | ${profitText}`,
      path: '/pages/statistics/statistics',
      imageUrl: '/images/share-statistics.jpg'
    };
  }
});
