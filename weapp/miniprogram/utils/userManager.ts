// 用户管理系统
import type { UserProfile, UserSettings, Achievement, UserLevel, CheckInRecord, CheckInStatus } from '../types/userTypes';
import { DataManager } from './dataManager';

export class UserManager {
  private static instance: UserManager;
  private dataManager: DataManager;
  private currentUser: UserProfile | null = null;
  
  private constructor() {
    this.dataManager = DataManager.getInstance();
    this.loadCurrentUser();
  }
  
  public static getInstance(): UserManager {
    if (!UserManager.instance) {
      UserManager.instance = new UserManager();
    }
    return UserManager.instance;
  }
  
  /**
   * 初始化用户（首次使用）
   */
  public async initializeUser(): Promise<UserProfile> {
    try {
      // 获取微信用户信息
      const userInfo = await this.getWechatUserInfo();
      
      const newUser: UserProfile = {
        id: this.generateUserId(),
        nickname: userInfo.nickName || '德州玩家',
        avatar: userInfo.avatarUrl || this.getDefaultAvatar(),
        level: 1,
        experience: 0,
        totalGames: 0,
        totalWins: 0,
        totalChipsWon: 0,
        createdAt: Date.now(),
        lastLoginAt: Date.now(),
        settings: this.getDefaultSettings(),
        achievements: []
      };
      
      this.currentUser = newUser;
      this.dataManager.saveUserProfile(newUser);
      

      
      return newUser;
    } catch (error) {
      console.error('[UserManager] 初始化用户失败:', error);
      throw error;
    }
  }
  
  /**
   * 获取当前用户
   */
  public getCurrentUser(): UserProfile | null {
    return this.currentUser;
  }
  
  /**
   * 更新用户信息
   */
  public updateUser(updates: Partial<UserProfile>): boolean {
    if (!this.currentUser) return false;
    
    this.currentUser = { ...this.currentUser, ...updates };
    return this.dataManager.saveUserProfile(this.currentUser);
  }
  
  /**
   * 更新用户设置
   */
  public updateSettings(settings: Partial<UserSettings>): boolean {
    if (!this.currentUser) return false;
    
    this.currentUser.settings = { ...this.currentUser.settings, ...settings };
    return this.dataManager.saveUserProfile(this.currentUser);
  }
  
  /**
   * 增加经验值
   */
  public addExperience(amount: number): { levelUp: boolean; newLevel?: number } {
    if (!this.currentUser) return { levelUp: false };
    
    const oldLevel = this.currentUser.level;
    this.currentUser.experience += amount;
    
    // 检查是否升级
    const newLevel = this.calculateLevel(this.currentUser.experience);
    const levelUp = newLevel > oldLevel;
    
    if (levelUp) {
      this.currentUser.level = newLevel;
      this.unlockAchievement(`level_${newLevel}`);
    }
    
    this.dataManager.saveUserProfile(this.currentUser);
    
    return { levelUp, newLevel: levelUp ? newLevel : undefined };
  }
  
  /**
   * 解锁成就
   */
  public unlockAchievement(achievementId: string): boolean {
    if (!this.currentUser) return false;
    
    // 检查是否已解锁
    if (this.currentUser.achievements.some(a => a.id === achievementId)) {
      return false;
    }
    
    const achievement = this.getAchievementById(achievementId);
    if (!achievement) return false;
    
    achievement.unlockedAt = Date.now();
    this.currentUser.achievements.push(achievement);
    
    // 给予经验奖励
    this.addExperience(50);
    
    this.dataManager.saveUserProfile(this.currentUser);
    
    // 显示成就解锁提示
    this.showAchievementUnlocked(achievement);
    
    return true;
  }
  

  

  

  
  /**
   * 获取用户等级信息
   */
  public getUserLevel(): UserLevel {
    if (!this.currentUser) return this.getLevelInfo(1);
    return this.getLevelInfo(this.currentUser.level);
  }
  
  /**
   * 检查成就进度
   */
  public checkAchievements(gameData: any): void {
    if (!this.currentUser) return;
    
    // 游戏次数成就
    if (this.currentUser.totalGames >= 10) this.unlockAchievement('games_10');
    if (this.currentUser.totalGames >= 50) this.unlockAchievement('games_50');
    if (this.currentUser.totalGames >= 100) this.unlockAchievement('games_100');
    
    // 胜利次数成就
    if (this.currentUser.totalWins >= 5) this.unlockAchievement('wins_5');
    if (this.currentUser.totalWins >= 25) this.unlockAchievement('wins_25');
    if (this.currentUser.totalWins >= 50) this.unlockAchievement('wins_50');
    
    // 筹码成就
    if (this.currentUser.totalChipsWon >= 10000) this.unlockAchievement('chips_10k');
    if (this.currentUser.totalChipsWon >= 50000) this.unlockAchievement('chips_50k');
    
    // 特殊成就
    if (gameData && gameData.bigWin && gameData.bigWin >= 5000) {
      this.unlockAchievement('big_win');
    }
  }
  
  // 私有方法
  private loadCurrentUser(): void {
    this.currentUser = this.dataManager.loadUserProfile();
    if (this.currentUser) {
      this.currentUser.lastLoginAt = Date.now();
      this.dataManager.saveUserProfile(this.currentUser);
    }
  }
  
  private async getWechatUserInfo(): Promise<any> {
    return new Promise((resolve) => {
      wx.getUserProfile({
        desc: '用于完善用户资料',
        success: (res) => resolve(res.userInfo),
        fail: () => resolve({ nickName: '德州玩家', avatarUrl: '' })
      });
    });
  }
  
  private generateUserId(): string {
    return `user_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
  
  private getDefaultAvatar(): string {
    return 'https://mmbiz.qpic.cn/mmbiz/icTdbqWNOwNRna42FI242Lcia07jQodd2FJGIYQfG0LAJGFxM4FbnQP6yfMxBgJ0F3YRqJCJ1aPAK2dQagdusBZg/0';
  }
  
  private getDefaultSettings(): UserSettings {
    return {
      soundEnabled: true,
      animationEnabled: true,
      autoAction: false,
      showAIHints: true,
      teachingMode: false,
      theme: 'default',
      language: 'zh'
    };
  }
  
  private calculateLevel(experience: number): number {
    // 简单的等级计算：每1000经验升1级
    return Math.floor(experience / 1000) + 1;
  }
  
  private getLevelInfo(level: number): UserLevel {
    const minExp = (level - 1) * 1000;
    const maxExp = level * 1000;
    
    return {
      level,
      name: this.getLevelName(level),
      minExperience: minExp,
      maxExperience: maxExp,
      benefits: this.getLevelBenefits(level)
    };
  }
  
  private getLevelName(level: number): string {
    const names = ['新手', '入门', '进阶', '熟练', '专家', '大师', '宗师'];
    const index = Math.min(Math.floor((level - 1) / 5), names.length - 1);
    return names[index];
  }
  
  private getLevelBenefits(level: number): string[] {
    const benefits = ['基础功能'];
    if (level >= 5) benefits.push('高级统计');
    if (level >= 10) benefits.push('AI深度分析');
    if (level >= 15) benefits.push('专属主题');
    return benefits;
  }
  

  
  private getAchievementById(id: string): Achievement | null {
    const achievements: { [key: string]: Achievement } = {
      'games_10': {
        id: 'games_10',
        name: '初出茅庐',
        description: '完成10局游戏',
        icon: '🎮',
        category: 'game'
      },
      'games_50': {
        id: 'games_50',
        name: '游戏达人',
        description: '完成50局游戏',
        icon: '🎯',
        category: 'game'
      },
      'wins_5': {
        id: 'wins_5',
        name: '小有成就',
        description: '获得5次胜利',
        icon: '🏆',
        category: 'skill'
      },
      'big_win': {
        id: 'big_win',
        name: '大获全胜',
        description: '单局获得5000+筹码',
        icon: '💰',
        category: 'special'
      },
      'checkin_week': {
        id: 'checkin_week',
        name: '坚持不懈',
        description: '连续签到7天',
        icon: '📅',
        category: 'social'
      },
      'checkin_month': {
        id: 'checkin_month',
        name: '持之以恒',
        description: '连续签到30天',
        icon: '🗓️',
        category: 'social'
      },
      'checkin_total_10': {
        id: 'checkin_total_10',
        name: '签到新手',
        description: '累计签到10天',
        icon: '✅',
        category: 'social'
      },
      'checkin_total_50': {
        id: 'checkin_total_50',
        name: '签到达人',
        description: '累计签到50天',
        icon: '📋',
        category: 'social'
      },
      'checkin_total_100': {
        id: 'checkin_total_100',
        name: '签到专家',
        description: '累计签到100天',
        icon: '🏅',
        category: 'social'
      }
    };
    
    return achievements[id] || null;
  }
  
  private showAchievementUnlocked(achievement: Achievement): void {
    wx.showModal({
      title: '🎉 成就解锁',
      content: `恭喜获得成就：${achievement.name}\n${achievement.description}`,
      showCancel: false,
      confirmText: '太棒了'
    });
  }

  /**
   * 获取签到状态
   */
  public getCheckInStatus(): CheckInStatus {
    const today = new Date().toISOString().split('T')[0];
    const checkInRecords = this.dataManager.loadData<CheckInRecord[]>('check_in_records', []);

    const lastRecord = checkInRecords[checkInRecords.length - 1];
    const hasCheckedToday = lastRecord && lastRecord.date === today;

    let consecutiveDays = 0;
    if (lastRecord) {
      if (hasCheckedToday) {
        consecutiveDays = lastRecord.day;
      } else {
        // 检查是否是昨天签到的
        const yesterday = new Date();
        yesterday.setDate(yesterday.getDate() - 1);
        const yesterdayStr = yesterday.toISOString().split('T')[0];

        if (lastRecord.date === yesterdayStr) {
          consecutiveDays = lastRecord.day;
        }
      }
    }

    const nextReward = this.getCheckInReward(consecutiveDays + 1);

    return {
      hasCheckedToday: !!hasCheckedToday,
      consecutiveDays,
      totalDays: checkInRecords.length,
      lastCheckInDate: lastRecord ? lastRecord.date : null,
      nextReward
    };
  }

  /**
   * 执行签到
   */
  public checkIn(): { success: boolean; reward?: { type: string; amount: number }; message: string } {
    const status = this.getCheckInStatus();

    if (status.hasCheckedToday) {
      return {
        success: false,
        message: '今天已经签到过了！'
      };
    }

    const today = new Date().toISOString().split('T')[0];
    const newDay = status.consecutiveDays + 1;
    const reward = this.getCheckInReward(newDay);

    // 创建签到记录
    const checkInRecord: CheckInRecord = {
      date: today,
      timestamp: Date.now(),
      day: newDay,
      reward
    };

    // 保存签到记录
    const checkInRecords = this.dataManager.loadData<CheckInRecord[]>('check_in_records', []);
    checkInRecords.push(checkInRecord);
    this.dataManager.saveData('check_in_records', checkInRecords);

    // 给予奖励
    if (reward.type === 'experience') {
      this.addExperience(reward.amount);
    }

    // 检查签到成就
    this.checkCheckInAchievements(newDay, checkInRecords.length);

    return {
      success: true,
      reward,
      message: `签到成功！获得${reward.amount}${reward.type === 'experience' ? '经验' : '金币'}`
    };
  }

  /**
   * 获取签到奖励
   */
  private getCheckInReward(day: number): { type: 'experience' | 'coins'; amount: number } {
    // 签到奖励规则：
    // 第1-3天：50经验
    // 第4-6天：75经验
    // 第7天：150经验（周奖励）
    // 第8天及以后：重复周期，但基础奖励递增

    const weekDay = ((day - 1) % 7) + 1;
    const weekNumber = Math.floor((day - 1) / 7) + 1;
    const baseBonus = (weekNumber - 1) * 10; // 每周递增10经验

    if (weekDay <= 3) {
      return { type: 'experience', amount: 50 + baseBonus };
    } else if (weekDay <= 6) {
      return { type: 'experience', amount: 75 + baseBonus };
    } else {
      return { type: 'experience', amount: 150 + baseBonus };
    }
  }

  /**
   * 检查签到相关成就
   */
  private checkCheckInAchievements(consecutiveDays: number, totalDays: number): void {
    // 连续签到成就
    if (consecutiveDays >= 7) this.unlockAchievement('checkin_week');
    if (consecutiveDays >= 30) this.unlockAchievement('checkin_month');

    // 总签到天数成就
    if (totalDays >= 10) this.unlockAchievement('checkin_total_10');
    if (totalDays >= 50) this.unlockAchievement('checkin_total_50');
    if (totalDays >= 100) this.unlockAchievement('checkin_total_100');
  }
}
