import { UserManager } from '../../utils/userManager';
import { StatisticsManager } from '../../utils/statisticsManager';

Page({
  data: {
    loaded: false,
    userProfile: null as any,
    winRateDisplay: '0.0',
    checkInStatus: {
      hasCheckedToday: false,
      consecutiveDays: 0,
      totalDays: 0,
      lastCheckInDate: null,
      nextReward: {
        type: 'experience',
        amount: 50
      }
    }
  },

  onLoad() {
    this.loadUserData();

    // 页面加载完成后触发动画
    setTimeout(() => {
      this.setData({ loaded: true });
    }, 100);
  },

  onShow() {
    // 每次显示页面时刷新用户数据
    this.loadUserData();
  },

  /**
   * 加载用户数据
   */
  loadUserData() {
    try {
      const userManager = UserManager.getInstance();
      const userProfile = userManager.getCurrentUser();

      if (userProfile) {
        // 获取统计数据
        const statsManager = StatisticsManager.getInstance();
        const statistics = statsManager.getStatistics();
        const winRateDisplay = statistics.winRate ? (statistics.winRate * 100).toFixed(1) : '0.0';

        // 获取签到状态
        const checkInStatus = userManager.getCheckInStatus();

        this.setData({
          userProfile,
          winRateDisplay,
          checkInStatus
        });
      }
    } catch (error) {
      console.error('[Index] 加载用户数据失败:', error);
    }
  },

  onStart() {
    // 添加触觉反馈
    wx.vibrateShort({
      type: 'light'
    });

    wx.navigateTo({
      url: '/pages/setup/setup',
      success: () => {
        console.log('[Index] 跳转到游戏设置页面');
      }
    });
  },

  goToStatistics() {
    this.handleCardClick('statistics', '/pages/statistics/statistics');
  },

  goToTeaching() {
    this.handleCardClick('teaching', '/pages/teaching/teaching');
  },





  /**
   * 统一处理卡片点击事件
   */
  handleCardClick(type: string, url: string) {
    // 触觉反馈
    wx.vibrateShort({
      type: 'light'
    });

    // 显示加载提示
    wx.showLoading({
      title: '加载中...',
      mask: true
    });

    // 延迟跳转，让用户看到点击反馈
    setTimeout(() => {
      wx.hideLoading();
      wx.navigateTo({
        url,
        success: () => {
          console.log(`[Index] 跳转到${type}页面成功`);
        },
        fail: (error) => {
          console.error(`[Index] 跳转到${type}页面失败:`, error);
          wx.showToast({
            title: '页面加载失败',
            icon: 'error'
          });
        }
      });
    }, 200);
  },

  /**
   * 签到功能
   */
  onCheckIn() {
    // 添加触觉反馈
    wx.vibrateShort({
      type: 'light'
    });

    try {
      const userManager = UserManager.getInstance();
      const result = userManager.checkIn();

      if (result.success) {
        // 显示签到成功提示
        wx.showToast({
          title: result.message,
          icon: 'success',
          duration: 2000
        });

        // 更新签到状态
        const checkInStatus = userManager.getCheckInStatus();
        this.setData({ checkInStatus });

        // 如果有奖励，显示奖励动画
        if (result.reward) {
          setTimeout(() => {
            wx.showModal({
              title: '🎉 签到奖励',
              content: `恭喜获得 ${result.reward!.amount} ${result.reward!.type === 'experience' ? '经验值' : '金币'}！`,
              showCancel: false,
              confirmText: '太棒了'
            });
          }, 1000);
        }
      } else {
        wx.showToast({
          title: result.message,
          icon: 'none'
        });
      }
    } catch (error) {
      console.error('[Index] 签到失败:', error);
      wx.showToast({
        title: '签到失败，请重试',
        icon: 'error'
      });
    }
  },

  /**
   * 查看我的战报
   */
  onBattleReports() {
    // 触觉反馈
    wx.vibrateShort({
      type: 'light'
    });

    wx.navigateTo({
      url: '/pages/battleReports/battleReports'
    });
  },

  /**
   * 残局复现功能
   */
  onEndgameReplay() {
    // 触觉反馈
    wx.vibrateShort({
      type: 'light'
    });

    wx.navigateTo({
      url: '/pages/endgameReplay/endgameReplay'
    });
  },

  onShareAppMessage() {
    return {
      title: '德州扑克训练 - 智能教学',
      path: '/pages/index/index',
      imageUrl: '/images/share-cover.jpg' // 如果有分享图片的话
    };
  }
});