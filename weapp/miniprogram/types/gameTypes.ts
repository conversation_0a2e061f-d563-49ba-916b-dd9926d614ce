// 游戏类型定义
import { GAME_CONSTANTS } from '../constants/gameConstants';

export interface Card {
  suit: string;
  value: string;
}

export interface Player {
  id: number;
  name: string;
  chips: number;
  hand: Card[];
  bet: number;
  folded: boolean;
  isAllIn: boolean;
  isDealer: boolean;
  isSmallBlind: boolean;
  isBigBlind: boolean;
  isAI: boolean;
  eliminated: boolean;
  playStyle?: string;
  playStyleLevel?: number;
  hasSquid?: boolean;
}

export interface AllInPlayer {
  playerId: number;
  allInAmount: number;
}

export interface SidePot {
  threshold: number;
  amount: number;
}

export interface ActionHistoryItem {
  playerId: number;
  playerName: string;
  action: string;
  amount?: number;
  reason?: string;
}

export interface SquidGameState {
  enabled: boolean;
  squidHolders: Set<number>;
  totalSquids: number;
  penaltyAmount: number;
  penaltyMultiplier: number;
}

export interface GameState {
  deck: Card[];
  players: Player[];
  communityCards: Card[];
  pot: number;
  sidePots: SidePot[];
  allInPlayers: AllInPlayer[];
  dealerIndex: number;
  smallBlindIndex: number;
  bigBlindIndex: number;
  currentPlayerIndex: number;
  currentBet: number;
  gamePhase: GamePhase;
  betRoundComplete: boolean;
  actionHistory: Record<string, ActionHistoryItem[]>;
  playersActedByPhase: Record<string, number[]>;
  _earlyWinPlayerId?: number;
  lastActedPlayerIndex?: number;
  squidGame?: SquidGameState;
}

export interface BettingAnimation {
  playerId: number;
  amount: number;
  key: string;
  start: { left: number; top: number };
  end: { left: number; top: number };
  fromPot?: boolean;
  toEnd?: boolean;
}

export interface PlayerActionBubbles {
  [key: string]: string;
}

export interface AIDecision {
  action: string;
  reason: string;
  amount?: number;
}

export interface AIScore {
  score: number;
  reason: string;
}

export interface EndHandResult {
  name: string;
  amount: number;
  type: string;
}

export interface ShowdownPlayer {
  name: string;
  hand: Card[];
  isWinner?: boolean;
  handEval?: string;
}

export interface NetworkRequestOptions {
  url: string;
  prompt: string;
  systemPrompt: string;
  model: string;
  version: string;
  proxy: boolean;
  timeout?: number;
  onSuccess: (response: any) => void;
  onFail: () => void;
}

export interface TimerConfig {
  id: string;
  duration: number;
  callback: () => void;
  interval?: boolean;
}

export interface GameConfig {
  playerCount: number;
  startingChips: number;
  smallBlind: number;
  bigBlind: number;
  actionWaitSeconds: number;
  endHandWaitSeconds: number;
  assistThinking: boolean;
}

// 游戏阶段类型
export type GamePhase = typeof GAME_CONSTANTS.GAME_PHASES[keyof typeof GAME_CONSTANTS.GAME_PHASES];

// 玩家操作类型
export type PlayerAction = typeof GAME_CONSTANTS.PLAYER_ACTIONS[keyof typeof GAME_CONSTANTS.PLAYER_ACTIONS];

// 玩家风格类型
export type PlayStyle = typeof GAME_CONSTANTS.PLAY_STYLES[number];
