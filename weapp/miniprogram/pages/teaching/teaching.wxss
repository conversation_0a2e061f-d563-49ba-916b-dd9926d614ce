/* AI教学页面样式 */
.container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400rpx;
  color: #999;
}

/* 学习进度概览 */
.progress-overview {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 20rpx;
  padding: 40rpx 30rpx;
  margin-bottom: 20rpx;
  color: white;
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.progress-title {
  font-size: 36rpx;
  font-weight: bold;
}

.progress-level {
  font-size: 24rpx;
  background-color: rgba(255, 255, 255, 0.2);
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
}

.progress-stats {
  display: flex;
  align-items: center;
  gap: 30rpx;
}

.progress-circle {
  width: 120rpx;
  height: 120rpx;
}

.circle-progress {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.circle-progress::before {
  content: '';
  position: absolute;
  width: 80rpx;
  height: 80rpx;
  background-color: white;
  border-radius: 50%;
  z-index: 1;
}

.progress-percent {
  font-size: 24rpx;
  font-weight: bold;
  color: #667eea;
  z-index: 2;
}

.progress-details {
  flex: 1;
}

.progress-text {
  font-size: 28rpx;
  display: block;
  margin-bottom: 10rpx;
}

.milestone-text {
  font-size: 24rpx;
  opacity: 0.8;
}

/* 快速操作 */
.quick-actions {
  display: flex;
  gap: 15rpx;
  margin-bottom: 20rpx;
}

.quick-btn {
  flex: 1;
  background-color: white;
  border: none;
  border-radius: 15rpx;
  padding: 30rpx 20rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.quick-btn.primary {
  background: linear-gradient(135deg, #28a745, #20c997);
  color: white;
}

.quick-btn.secondary {
  color: #667eea;
}

.btn-icon {
  font-size: 36rpx;
  margin-bottom: 10rpx;
}

.btn-text {
  font-size: 26rpx;
  font-weight: bold;
}

/* 设置区块 */
.settings-section {
  background-color: white;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 25rpx;
  padding-bottom: 15rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.settings-list {
  space-y: 20rpx;
}

.setting-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 2rpx solid #f8f9fa;
}

.setting-item:last-child {
  border-bottom: none;
}

.setting-label {
  font-size: 28rpx;
  color: #333;
}

.picker-text {
  font-size: 26rpx;
  color: #667eea;
  padding: 10rpx 20rpx;
  background-color: #f8f9fa;
  border-radius: 8rpx;
}

/* 课程列表 */
.lessons-section {
  background-color: white;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.lessons-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 20rpx;
}

.lesson-card {
  background-color: #f8f9fa;
  border-radius: 15rpx;
  padding: 25rpx;
  transition: transform 0.2s ease;
}

.lesson-card:active {
  transform: scale(0.98);
}

.lesson-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 15rpx;
}

.lesson-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  flex: 1;
  margin-right: 15rpx;
}

.lesson-difficulty {
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  color: white;
  font-size: 20rpx;
}

.lesson-description {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
  margin-bottom: 15rpx;
  display: block;
}

.lesson-topics {
  display: flex;
  flex-wrap: wrap;
  gap: 8rpx;
  margin-bottom: 15rpx;
}

.topic-tag {
  background-color: rgba(102, 126, 234, 0.1);
  color: #667eea;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  font-size: 22rpx;
}

.lesson-progress {
  display: flex;
  align-items: center;
  gap: 15rpx;
}

.progress-bar {
  flex: 1;
  height: 8rpx;
  background-color: #e9ecef;
  border-radius: 4rpx;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background-color: #667eea;
  border-radius: 4rpx;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 22rpx;
  color: #666;
  min-width: 50rpx;
}

/* 操作按钮 */
.action-buttons {
  display: flex;
  gap: 15rpx;
  margin-top: 30rpx;
}

.action-btn {
  flex: 1;
  padding: 25rpx;
  border-radius: 15rpx;
  font-size: 28rpx;
  border: none;
}

.action-btn.secondary {
  background-color: white;
  color: #667eea;
  border: 2rpx solid #667eea;
}

.action-btn.danger {
  background-color: #dc3545;
  color: white;
}

/* 弹窗样式 */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background-color: white;
  border-radius: 20rpx;
  width: 85%;
  max-width: 600rpx;
  max-height: 80%;
  overflow: hidden;
}

.lesson-detail {
  max-height: 70vh;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.modal-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.modal-close {
  font-size: 48rpx;
  color: #999;
}

.modal-body {
  padding: 30rpx;
  max-height: 50vh;
  overflow-y: auto;
}

.modal-footer {
  display: flex;
  padding: 20rpx 30rpx 30rpx;
  gap: 15rpx;
}

.modal-btn {
  flex: 1;
  padding: 20rpx;
  border-radius: 10rpx;
  font-size: 28rpx;
  border: none;
}

.modal-btn.cancel {
  background-color: #f8f9fa;
  color: #666;
}

.modal-btn.confirm {
  background-color: #667eea;
  color: white;
}

/* 课程详情 */
.lesson-info {
  space-y: 20rpx;
}

.lesson-meta {
  display: flex;
  gap: 30rpx;
  margin-bottom: 20rpx;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.meta-label {
  font-size: 24rpx;
  color: #666;
}

.meta-value {
  font-size: 24rpx;
  font-weight: bold;
}

.lesson-desc {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
  margin-bottom: 25rpx;
  display: block;
}

.topics-list {
  margin-top: 25rpx;
}

.topics-title {
  font-size: 26rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 15rpx;
  display: block;
}

.topic-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 10rpx;
}

.topic-bullet {
  color: #667eea;
  margin-right: 12rpx;
  font-weight: bold;
}

.topic-text {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
}
