{"description": "项目配置文件", "miniprogramRoot": "miniprogram/", "compileType": "miniprogram", "setting": {"useCompilerPlugins": ["typescript"], "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "es6": true, "enhance": true, "coverView": false, "postcss": false, "minified": false, "showShadowRootInWxmlPanel": false, "packNpmRelationList": [], "ignoreUploadUnusedFiles": true, "compileHotReLoad": false, "skylineRenderEnable": true}, "simulatorType": "wechat", "simulatorPluginLibVersion": {}, "condition": {}, "srcMiniprogramRoot": "miniprogram/", "editorSetting": {"tabIndent": "insertSpaces", "tabSize": 2}, "libVersion": "trial", "packOptions": {"ignore": [], "include": []}, "appid": "wx10d6660355896c27"}