// 统计系统相关类型定义
import type { Card, Player, GamePhase } from './gameTypes';

export interface GameRecord {
  id: string;
  startTime: number;
  endTime: number;
  duration: number; // 游戏时长(秒)
  playerCount: number;
  startingChips: number;
  smallBlind: number;
  bigBlind: number;
  squidMode: boolean;
  
  // 玩家结果
  playerResult: {
    finalChips: number;
    profit: number; // 盈亏
    position: number; // 排名
    handsPlayed: number;
    handsWon: number;
    totalBet: number;
    biggestPot: number;
  };
  
  // 详细手牌记录
  hands: HandRecord[];
  
  // AI使用情况
  aiHintsUsed: number;
  teachingModeUsed: boolean;
}

export interface HandRecord {
  handNumber: number;
  startTime: number;
  endTime: number;
  dealerPosition: number;
  playerPosition: number;
  
  // 手牌信息
  holeCards: Card[];
  communityCards: Card[];
  finalHand?: {
    cards: Card[];
    rank: string;
    strength: number;
  };
  
  // 行动记录
  actions: ActionRecord[];
  
  // 结果
  result: {
    won: boolean;
    profit: number;
    potSize: number;
    showdown: boolean;
  };
  
  // AI分析
  aiAnalysis?: {
    optimalActions: string[];
    mistakes: string[];
    score: number;
    difficulty: 'easy' | 'medium' | 'hard';
  };
}

export interface ActionRecord {
  phase: GamePhase;
  action: string;
  amount?: number;
  timestamp: number;
  position: number;
  
  // AI评估
  aiEvaluation?: {
    isOptimal: boolean;
    score: number;
    reason: string;
    betterAction?: string;
  };
}

// 统计数据
export interface GameStatistics {
  // 基础统计
  totalGames: number;
  totalHands: number;
  totalWins: number;
  totalProfit: number;
  totalPlayTime: number; // 总游戏时间(秒)
  
  // 胜率统计
  winRate: number; // 游戏胜率
  handWinRate: number; // 手牌胜率
  showdownWinRate: number; // 摊牌胜率
  
  // 盈利统计
  avgProfit: number; // 平均每局盈利
  biggestWin: number; // 最大单局盈利
  biggestLoss: number; // 最大单局亏损
  profitTrend: number[]; // 盈利趋势(最近30局)
  
  // 操作习惯
  vpip: number; // 入池率 (Voluntarily Put In Pot)
  pfr: number; // 翻牌前加注率 (Pre-Flop Raise)
  aggression: number; // 激进度
  foldToRaise: number; // 面对加注弃牌率
  
  // 位置统计
  positionStats: {
    [position: string]: {
      handsPlayed: number;
      winRate: number;
      avgProfit: number;
    };
  };
  
  // 阶段统计
  phaseStats: {
    [phase: string]: {
      foldRate: number;
      betRate: number;
      raiseRate: number;
      callRate: number;
    };
  };
  
  // AI使用统计
  aiStats: {
    hintsUsed: number;
    hintsFollowed: number;
    teachingModeGames: number;
    avgAIScore: number;
  };
}

// 趋势数据
export interface TrendData {
  date: string; // YYYY-MM-DD
  games: number;
  wins: number;
  profit: number;
  winRate: number;
  avgScore: number;
}

// 排行榜数据
export interface LeaderboardEntry {
  rank: number;
  userId: string;
  nickname: string;
  avatar: string;
  score: number;
  games: number;
  winRate: number;
  level: number;
}

// 对比分析
export interface ComparisonData {
  period: 'week' | 'month' | 'all';
  current: Partial<GameStatistics>;
  previous: Partial<GameStatistics>;
  improvement: {
    [key: string]: {
      value: number;
      percentage: number;
      trend: 'up' | 'down' | 'stable';
    };
  };
}
