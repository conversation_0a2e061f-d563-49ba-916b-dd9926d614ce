/* setup.wxss */
.setup-container {
  max-width: 600rpx;
  margin: 60rpx auto;
  background: #fff;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 24rpx rgba(0,0,0,0.08);
  padding: 40rpx 24rpx;
}
.game-title {
  text-align: center;
  font-size: 36rpx;
  color: #2c3e50;
  margin-bottom: 40rpx;
}
.setup-controls {
  display: flex;
  flex-direction: column;
  gap: 32rpx;
}
.setup-row {
  display: flex;
  align-items: center;
  gap: 16rpx;
  margin-bottom: 12rpx;
}
input[type="number"] {
  border: 1rpx solid #ccc;
  border-radius: 8rpx;
  padding: 8rpx 16rpx;
  font-size: 28rpx;
  width: 120rpx;
}
button[type="primary"] {
  background: #27ae60;
  color: #fff;
  border-radius: 8rpx;
  font-size: 32rpx;
  padding: 16rpx 0;
  width: 100%;
  margin-top: 24rpx;
}

/* 鱿鱼模式相关样式 */
.squid-penalty-row {
  background: rgba(255, 107, 107, 0.1);
  padding: 16rpx;
  border-radius: 8rpx;
  border-left: 4rpx solid #ff6b6b;
  flex-wrap: wrap;
}

.penalty-hint {
  font-size: 24rpx;
  color: #ff6b6b;
  font-weight: bold;
  width: 100%;
  margin-top: 8rpx;
}

/* AI教学特殊样式 */
.teaching-mode-section {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 20rpx;
  padding: 24rpx;
  margin: 16rpx 0;
  position: relative;
  overflow: hidden;
  box-shadow: 0 8rpx 32rpx rgba(102, 126, 234, 0.3);
  transition: all 0.3s ease;
}

.teaching-mode-section.unlocked {
  background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
  box-shadow: 0 8rpx 32rpx rgba(76, 175, 80, 0.4);
  animation: unlocked-glow 2s ease-in-out infinite alternate;
}

.teaching-mode-section.locked {
  background: linear-gradient(135deg, #9e9e9e 0%, #757575 100%);
  box-shadow: 0 8rpx 32rpx rgba(158, 158, 158, 0.3);
}

@keyframes unlocked-glow {
  0% { box-shadow: 0 8rpx 32rpx rgba(76, 175, 80, 0.4); }
  100% { box-shadow: 0 12rpx 40rpx rgba(76, 175, 80, 0.6); }
}

.teaching-mode-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.teaching-mode-section.unlocked::before {
  animation: shine 3s ease-in-out infinite;
}

@keyframes shine {
  0% { left: -100%; }
  50% { left: 100%; }
  100% { left: 100%; }
}

.teaching-header {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.teaching-icon-wrapper {
  position: relative;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.teaching-icon {
  font-size: 32rpx;
  z-index: 2;
  position: relative;
}

.teaching-glow {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 80rpx;
  height: 80rpx;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
  border-radius: 50%;
  animation: glow-pulse 2s ease-in-out infinite;
}

@keyframes glow-pulse {
  0%, 100% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 0.7;
  }
  50% {
    transform: translate(-50%, -50%) scale(1.2);
    opacity: 1;
  }
}

.teaching-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4rpx;
}

.teaching-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #fff;
}

.teaching-desc {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.4;
}

.teaching-control {
  display: flex;
  align-items: center;
}

.unlock-btn {
  background: rgba(255, 255, 255, 0.2) !important;
  border: 2rpx solid rgba(255, 255, 255, 0.3) !important;
  border-radius: 50rpx !important;
  padding: 8rpx 16rpx !important;
  display: flex !important;
  align-items: center !important;
  gap: 6rpx !important;
  transition: all 0.3s ease !important;
}

.unlock-btn:active {
  background: rgba(255, 255, 255, 0.3) !important;
  transform: scale(0.95);
}

.unlock-icon {
  font-size: 20rpx;
  color: #fff;
}

.unlock-text {
  font-size: 24rpx;
  color: #fff;
  font-weight: bold;
}

.teaching-features {
  display: flex;
  gap: 12rpx;
  margin-top: 16rpx;
  flex-wrap: wrap;
}

.feature-tag {
  background: rgba(255, 255, 255, 0.2);
  color: #fff;
  padding: 6rpx 12rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.3);
}

.unlock-hint {
  margin-top: 16rpx;
  text-align: center;
}

.hint-text {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.9);
  background: rgba(255, 255, 255, 0.1);
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.2);
}

/* 功能价值展示 */
.teaching-value {
  margin-top: 20rpx;
  padding-top: 20rpx;
  border-top: 1rpx solid rgba(255, 255, 255, 0.2);
}

.value-stats {
  display: flex;
  justify-content: space-around;
  margin-bottom: 16rpx;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4rpx;
}

.stat-number {
  font-size: 32rpx;
  font-weight: bold;
  color: #fff;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
}

.stat-label {
  font-size: 20rpx;
  color: rgba(255, 255, 255, 0.8);
}

.value-desc {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.desc-text {
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.4;
  padding: 4rpx 0;
}

/* 已解锁提醒 */
.unlocked-reminder {
  margin-top: 16rpx;
  text-align: center;
}

.reminder-text {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.9);
  background: rgba(255, 255, 255, 0.15);
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.3);
}