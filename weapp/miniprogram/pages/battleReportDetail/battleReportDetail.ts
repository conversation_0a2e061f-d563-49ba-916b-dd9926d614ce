// 战报详情页面
import { BattleReportManager } from '../../utils/battleReportManager';
import type { BattleReport } from '../../utils/battleReportManager';

Page({
  data: {
    battleReport: null as BattleReport | null,
    loading: true,
    activeTab: 'overview' as 'overview' | 'analysis' | 'moments',
    
    // 格式化数据
    formattedDate: '',
    gameDurationText: '',
    ratingColor: '',
    ratingText: ''
  },

  onLoad(options: any) {
    const { reportId } = options;
    if (reportId) {
      this.loadBattleReport(reportId);
    } else {
      wx.showToast({
        title: '战报ID无效',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }
  },

  /**
   * 加载战报详情
   */
  loadBattleReport(reportId: string) {
    this.setData({ loading: true });

    try {
      const battleReportManager = BattleReportManager.getInstance();
      const report = battleReportManager.getBattleReport(reportId);
      
      if (!report) {
        wx.showToast({
          title: '战报不存在',
          icon: 'none'
        });
        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
        return;
      }

      // 格式化数据
      const formattedDate = new Date(report.timestamp).toLocaleString();
      const gameDurationText = this.formatDuration(report.gameResult.gameDuration);
      const ratingColor = this.getRatingColor(report.aiAnalysis.overallRating);
      const ratingText = this.getRatingText(report.aiAnalysis.overallRating);

      this.setData({
        battleReport: report,
        formattedDate,
        gameDurationText,
        ratingColor,
        ratingText,
        loading: false
      });
    } catch (error) {
      console.error('[BattleReportDetail] 加载战报失败:', error);
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }
  },

  /**
   * 切换标签页
   */
  switchTab(e: any) {
    const { tab } = e.currentTarget.dataset;
    this.setData({ activeTab: tab });
  },

  /**
   * 分享战报
   */
  shareBattleReport() {
    const report = this.data.battleReport;
    if (!report) return;

    const shareText = this.generateShareText(report);
    
    wx.setClipboardData({
      data: shareText,
      success: () => {
        wx.showToast({
          title: '战报已复制到剪贴板',
          icon: 'success'
        });
      }
    });
  },

  /**
   * 生成分享文本
   */
  generateShareText(report: BattleReport): string {
    const date = new Date(report.timestamp).toLocaleDateString();
    const rating = report.aiAnalysis.overallRating;
    const result = report.playerPerformance.chipsWon > 0 ? '盈利' : '亏损';
    const amount = Math.abs(report.playerPerformance.chipsWon);
    
    let shareText = `🎮 德州扑克战报详情
📅 日期: ${date}
🏆 结果: ${result} ${amount} 筹码 (第${report.playerPerformance.finalPosition}名)
⭐ AI评分: ${rating}/100分 (${this.getRatingText(rating)})

📊 关键数据:
• 入池率: ${report.playerPerformance.vpip}%
• 加注率: ${report.playerPerformance.pfr}%
• 激进度: ${report.playerPerformance.aggressionFactor}

💪 优势:
${report.aiAnalysis.strengths.slice(0, 2).map(s => `• ${s}`).join('\n')}

⚠️ 待改进:
${report.aiAnalysis.weaknesses.slice(0, 2).map(w => `• ${w}`).join('\n')}

${report.summary}

来德州扑克智能训练一起提升技术吧！`;

    return shareText;
  },

  /**
   * 格式化时长
   */
  formatDuration(minutes: number): string {
    if (minutes < 60) {
      return `${minutes}分钟`;
    } else {
      const hours = Math.floor(minutes / 60);
      const mins = minutes % 60;
      return `${hours}小时${mins}分钟`;
    }
  },

  /**
   * 获取评分颜色
   */
  getRatingColor(rating: number): string {
    if (rating >= 80) return '#4CAF50';
    if (rating >= 60) return '#FF9800';
    return '#F44336';
  },

  /**
   * 获取评分文本
   */
  getRatingText(rating: number): string {
    if (rating >= 90) return '卓越';
    if (rating >= 80) return '优秀';
    if (rating >= 70) return '良好';
    if (rating >= 60) return '及格';
    return '有待提高';
  },

  /**
   * 获取决策结果颜色
   */
  getDecisionColor(result: string): string {
    switch (result) {
      case 'good': return '#4CAF50';
      case 'neutral': return '#FF9800';
      case 'poor': return '#F44336';
      default: return '#666';
    }
  },

  /**
   * 获取决策结果文本
   */
  getDecisionText(result: string): string {
    switch (result) {
      case 'good': return '✅ 正确';
      case 'neutral': return '⚪ 一般';
      case 'poor': return '❌ 错误';
      default: return '未知';
    }
  },

  /**
   * 获取影响级别颜色
   */
  getImpactColor(impact: string): string {
    switch (impact) {
      case 'high': return '#F44336';
      case 'medium': return '#FF9800';
      case 'low': return '#4CAF50';
      default: return '#666';
    }
  },

  /**
   * 获取影响级别文本
   */
  getImpactText(impact: string): string {
    switch (impact) {
      case 'high': return '高影响';
      case 'medium': return '中影响';
      case 'low': return '低影响';
      default: return '未知';
    }
  },

  /**
   * 页面分享
   */
  onShareAppMessage() {
    const report = this.data.battleReport;
    const title = report ? 
      `我的德州扑克战报 - AI评分${report.aiAnalysis.overallRating}分` : 
      '德州扑克智能战报分析';
      
    return {
      title,
      path: '/pages/index/index',
      imageUrl: '/images/share-battle-report-detail.jpg'
    };
  }
});
