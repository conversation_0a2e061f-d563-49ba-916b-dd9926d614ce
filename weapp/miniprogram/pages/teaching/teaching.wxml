<!--AI教学页面-->
<view class="container">
  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading">
    <text>加载中...</text>
  </view>

  <!-- 主要内容 -->
  <view wx:else class="content">
    <!-- 学习进度概览 -->
    <view class="progress-overview">
      <view class="progress-header">
        <text class="progress-title">🎓 学习进度</text>
        <text class="progress-level">{{learningProgress.currentLevel}}</text>
      </view>
      <view class="progress-stats">
        <view class="progress-circle">
          <view class="circle-progress" style="background: conic-gradient(#667eea {{progressPercent}}%, #e9ecef 0%)">
            <text class="progress-percent">{{progressPercent}}%</text>
          </view>
        </view>
        <view class="progress-details">
          <text class="progress-text">已完成 {{learningProgress.completedLessons}}/{{learningProgress.totalLessons}} 课程</text>
          <text class="milestone-text">下一个目标: {{learningProgress.nextMilestone}}</text>
        </view>
      </view>
    </view>

    <!-- 快速操作 -->
    <view class="quick-actions">
      <button class="quick-btn primary" bindtap="startQuickPractice">
        <text class="btn-icon">⚡</text>
        <text class="btn-text">快速练习</text>
      </button>
      <button class="quick-btn secondary" bindtap="viewLearningStats">
        <text class="btn-icon">📊</text>
        <text class="btn-text">学习统计</text>
      </button>
    </view>

    <!-- 教学设置 -->
    <view class="settings-section">
      <view class="section-title">教学设置</view>
      <view class="settings-list">
        <view class="setting-item">
          <text class="setting-label">实时提示</text>
          <switch checked="{{teachingSettings.realTimeHints}}" 
                  bindchange="onSettingChange" data-setting="realTimeHints"/>
        </view>
        <view class="setting-item">
          <text class="setting-label">详细分析</text>
          <switch checked="{{teachingSettings.detailedAnalysis}}" 
                  bindchange="onSettingChange" data-setting="detailedAnalysis"/>
        </view>
        <view class="setting-item">
          <text class="setting-label">错误纠正</text>
          <switch checked="{{teachingSettings.mistakeCorrection}}" 
                  bindchange="onSettingChange" data-setting="mistakeCorrection"/>
        </view>
        <view class="setting-item">
          <text class="setting-label">难度级别</text>
          <picker mode="selector" range="['初级', '中级', '高级']"
                  bindchange="onDifficultyChange"
                  value="{{difficultyIndex}}">
            <text class="picker-text">{{getDifficultyLabel(teachingSettings.difficultyLevel)}}</text>
          </picker>
        </view>
      </view>
    </view>

    <!-- 课程列表 -->
    <view class="lessons-section">
      <view class="section-title">教学课程</view>
      <view class="lessons-grid">
        <block wx:for="{{lessons}}" wx:key="id">
          <view class="lesson-card" bindtap="selectLesson" data-index="{{index}}">
            <view class="lesson-header">
              <text class="lesson-title">{{item.title}}</text>
              <view class="lesson-difficulty" style="background-color: {{getDifficultyColor(item.difficulty)}}">
                <text>{{getDifficultyLabel(item.difficulty)}}</text>
              </view>
            </view>
            <text class="lesson-description">{{item.description}}</text>
            <view class="lesson-topics">
              <block wx:for="{{item.topics}}" wx:key="*this" wx:for-item="topic">
                <text class="topic-tag">{{topic}}</text>
              </block>
            </view>
            <view class="lesson-progress">
              <view class="progress-bar">
                <view class="progress-fill" style="width: {{getLessonProgress(item)}}%"></view>
              </view>
              <text class="progress-text">{{getLessonProgress(item)}}%</text>
            </view>
          </view>
        </block>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="action-buttons">
      <button class="action-btn secondary" bindtap="shareLearningProgress">分享进度</button>
      <button class="action-btn danger" bindtap="resetProgress">重置进度</button>
    </view>
  </view>

  <!-- 课程详情弹窗 -->
  <view class="modal" wx:if="{{showLessonDetail}}" bindtap="closeLessonDetail">
    <view class="modal-content lesson-detail" catchtap="">
      <view class="modal-header">
        <text class="modal-title">{{selectedLesson.title}}</text>
        <text class="modal-close" bindtap="closeLessonDetail">×</text>
      </view>
      <view class="modal-body" wx:if="{{selectedLesson}}">
        <view class="lesson-info">
          <view class="lesson-meta">
            <view class="meta-item">
              <text class="meta-label">难度:</text>
              <text class="meta-value" style="color: {{getDifficultyColor(selectedLesson.difficulty)}}">
                {{getDifficultyLabel(selectedLesson.difficulty)}}
              </text>
            </view>
            <view class="meta-item">
              <text class="meta-label">主题数:</text>
              <text class="meta-value">{{selectedLesson.topics.length}}</text>
            </view>
          </view>
          <text class="lesson-desc">{{selectedLesson.description}}</text>
          <view class="topics-list">
            <text class="topics-title">学习内容:</text>
            <block wx:for="{{selectedLesson.topics}}" wx:key="*this" wx:for-item="topic">
              <view class="topic-item">
                <text class="topic-bullet">•</text>
                <text class="topic-text">{{topic}}</text>
              </view>
            </block>
          </view>
        </view>
      </view>
      <view class="modal-footer">
        <button class="modal-btn cancel" bindtap="closeLessonDetail">取消</button>
        <button class="modal-btn confirm" bindtap="startLesson">开始学习</button>
      </view>
    </view>
  </view>
</view>
