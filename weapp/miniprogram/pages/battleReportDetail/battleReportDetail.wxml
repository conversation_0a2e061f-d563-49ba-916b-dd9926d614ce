<!--战报详情页面-->
<view class="battle-report-detail-container">
  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading-container">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载战报详情中...</text>
  </view>

  <!-- 战报内容 -->
  <view wx:else class="report-detail-content">
    <!-- 头部信息 -->
    <view class="report-header">
      <view class="header-info">
        <text class="report-title">📊 战报详情</text>
        <text class="report-date">{{formattedDate}}</text>
      </view>
      <view class="header-actions">
        <button class="share-btn" bindtap="shareBattleReport">
          <text class="share-icon">📤</text>
          <text class="share-text">分享</text>
        </button>
      </view>
    </view>

    <!-- 游戏概览 -->
    <view class="game-overview">
      <view class="overview-title">🎮 游戏概览</view>
      <view class="overview-grid">
        <view class="overview-item">
          <text class="item-label">玩家数量</text>
          <text class="item-value">{{battleReport.gameSettings.playerCount}}人</text>
        </view>
        <view class="overview-item">
          <text class="item-label">游戏时长</text>
          <text class="item-value">{{gameDurationText}}</text>
        </view>
        <view class="overview-item">
          <text class="item-label">总手牌数</text>
          <text class="item-value">{{battleReport.gameResult.totalHands}}手</text>
        </view>
        <view class="overview-item">
          <text class="item-label">最终排名</text>
          <text class="item-value">第{{battleReport.playerPerformance.finalPosition}}名</text>
        </view>
      </view>
    </view>

    <!-- 标签页导航 -->
    <view class="tab-navigation">
      <view 
        class="tab-item {{activeTab === 'overview' ? 'active' : ''}}"
        bindtap="switchTab"
        data-tab="overview"
      >
        <text class="tab-text">总览</text>
      </view>
      <view 
        class="tab-item {{activeTab === 'analysis' ? 'active' : ''}}"
        bindtap="switchTab"
        data-tab="analysis"
      >
        <text class="tab-text">分析</text>
      </view>
      <view 
        class="tab-item {{activeTab === 'moments' ? 'active' : ''}}"
        bindtap="switchTab"
        data-tab="moments"
      >
        <text class="tab-text">关键时刻</text>
      </view>
    </view>

    <!-- 总览标签页 -->
    <view wx:if="{{activeTab === 'overview'}}" class="tab-content">
      <!-- AI评分 -->
      <view class="rating-section">
        <view class="section-title">⭐ AI综合评分</view>
        <view class="rating-display">
          <view class="rating-circle" style="border-color: {{ratingColor}}">
            <text class="rating-score" style="color: {{ratingColor}}">{{battleReport.aiAnalysis.overallRating}}</text>
            <text class="rating-total">/ 100</text>
          </view>
          <view class="rating-info">
            <text class="rating-level" style="color: {{ratingColor}}">{{ratingText}}</text>
            <text class="rating-desc">综合表现评价</text>
          </view>
        </view>
      </view>

      <!-- 盈亏结果 -->
      <view class="result-section">
        <view class="section-title">💰 盈亏结果</view>
        <view class="result-display">
          <text class="result-amount {{battleReport.playerPerformance.chipsWon > 0 ? 'profit' : 'loss'}}">
            {{battleReport.playerPerformance.chipsWon > 0 ? '+' : ''}}{{battleReport.playerPerformance.chipsWon}}
          </text>
          <text class="result-unit">筹码</text>
        </view>
      </view>

      <!-- 关键数据 -->
      <view class="stats-section">
        <view class="section-title">📊 关键数据</view>
        <view class="stats-grid">
          <view class="stat-item">
            <text class="stat-value">{{battleReport.playerPerformance.vpip}}%</text>
            <text class="stat-label">入池率 (VPIP)</text>
          </view>
          <view class="stat-item">
            <text class="stat-value">{{battleReport.playerPerformance.pfr}}%</text>
            <text class="stat-label">加注率 (PFR)</text>
          </view>
          <view class="stat-item">
            <text class="stat-value">{{battleReport.playerPerformance.aggressionFactor}}</text>
            <text class="stat-label">激进因子</text>
          </view>
          <view class="stat-item">
            <text class="stat-value">{{battleReport.playerPerformance.winRate}}%</text>
            <text class="stat-label">胜率</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 分析标签页 -->
    <view wx:if="{{activeTab === 'analysis'}}" class="tab-content">
      <!-- 优势分析 -->
      <view class="analysis-section">
        <view class="section-title">💪 优势分析</view>
        <view class="analysis-list">
          <view 
            wx:for="{{battleReport.aiAnalysis.strengths}}" 
            wx:key="index" 
            class="analysis-item strength"
          >
            <text class="analysis-icon">✅</text>
            <text class="analysis-text">{{item}}</text>
          </view>
        </view>
      </view>

      <!-- 待改进分析 -->
      <view class="analysis-section">
        <view class="section-title">⚠️ 待改进</view>
        <view class="analysis-list">
          <view 
            wx:for="{{battleReport.aiAnalysis.weaknesses}}" 
            wx:key="index" 
            class="analysis-item weakness"
          >
            <text class="analysis-icon">⚠️</text>
            <text class="analysis-text">{{item}}</text>
          </view>
        </view>
      </view>

      <!-- 改进建议 -->
      <view class="analysis-section">
        <view class="section-title">💡 改进建议</view>
        <view class="analysis-list">
          <view 
            wx:for="{{battleReport.aiAnalysis.recommendations}}" 
            wx:key="index" 
            class="analysis-item recommendation"
          >
            <text class="analysis-icon">💡</text>
            <text class="analysis-text">{{item}}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 关键时刻标签页 -->
    <view wx:if="{{activeTab === 'moments'}}" class="tab-content">
      <view class="moments-section">
        <view class="section-title">🎯 关键决策时刻</view>
        <view class="moments-list">
          <view 
            wx:for="{{battleReport.aiAnalysis.keyMoments}}" 
            wx:key="index" 
            class="moment-item"
          >
            <view class="moment-header">
              <text class="moment-title">第{{item.handNumber}}手 - {{item.phase}}</text>
              <view class="impact-badge {{item.impact}}">
                <text class="impact-text">{{getImpactText(item.impact)}}</text>
              </view>
            </view>
            <view class="moment-content">
              <text class="moment-description">{{item.description}}</text>
              <view class="moment-actions">
                <view class="action-comparison">
                  <view class="actual-action">
                    <text class="action-label">实际操作:</text>
                    <text class="action-value">{{item.playerAction}}</text>
                  </view>
                  <view class="optimal-action">
                    <text class="action-label">最优操作:</text>
                    <text class="action-value">{{item.optimalAction}}</text>
                  </view>
                </view>
                <text class="moment-analysis">{{item.analysis}}</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 战报摘要 -->
    <view class="summary-section">
      <view class="section-title">📝 战报摘要</view>
      <text class="summary-text">{{battleReport.summary}}</text>
    </view>
  </view>
</view>
